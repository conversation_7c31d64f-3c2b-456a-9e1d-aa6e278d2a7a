'use client'
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// react/index.ts
var react_exports = {};
__export(react_exports, {
  experimental_useObject: () => experimental_useObject,
  useAssistant: () => useAssistant,
  useChat: () => useChat,
  useCompletion: () => useCompletion
});
module.exports = __toCommonJS(react_exports);
var import_react = require("@ai-sdk/react");
var useChat = import_react.useChat;
var useCompletion = import_react.useCompletion;
var useAssistant = import_react.useAssistant;
var experimental_useObject = import_react.experimental_useObject;
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  experimental_useObject,
  useAssistant,
  useChat,
  useCompletion
});
//# sourceMappingURL=index.js.map