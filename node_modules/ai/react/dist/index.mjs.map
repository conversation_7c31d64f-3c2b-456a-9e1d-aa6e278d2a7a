{"version": 3, "sources": ["../index.ts"], "sourcesContent": ["import {\n  useChat as useChatReact,\n  useCompletion as useCompletionReact,\n  useAssistant as useAssistantReact,\n  experimental_useObject as experimental_useObjectReact,\n} from '@ai-sdk/react';\n\n/**\n * @deprecated Use `@ai-sdk/react` instead.\n */\nexport const useChat = useChatReact;\n\n/**\n * @deprecated Use `@ai-sdk/react` instead.\n */\nexport const useCompletion = useCompletionReact;\n\n/**\n * @deprecated Use `@ai-sdk/react` instead.\n */\nexport const useAssistant = useAssistantReact;\n\n/**\n * @deprecated Use `@ai-sdk/react` instead.\n */\nexport const experimental_useObject = experimental_useObjectReact;\n\nexport type {\n  /**\n   * @deprecated Use `@ai-sdk/react` instead.\n   */\n  CreateMessage,\n\n  /**\n   * @deprecated Use `@ai-sdk/react` instead.\n   */\n  Message,\n\n  /**\n   * @deprecated Use `@ai-sdk/react` instead.\n   */\n  UseChatOptions,\n\n  /**\n   * @deprecated Use `@ai-sdk/react` instead.\n   */\n  UseChatHelpers,\n} from '@ai-sdk/react';\n"], "mappings": ";;;AAAA;AAAA,EACE,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,gBAAgB;AAAA,EAChB,0BAA0B;AAAA,OACrB;AAKA,IAAM,UAAU;AAKhB,IAAM,gBAAgB;AAKtB,IAAM,eAAe;AAKrB,IAAM,yBAAyB;", "names": []}