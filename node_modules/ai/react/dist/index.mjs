'use client'

// react/index.ts
import {
  useChat as useChatReact,
  useCompletion as useCompletionReact,
  useAssistant as useAssistantReact,
  experimental_useObject as experimental_useObjectReact
} from "@ai-sdk/react";
var useChat = useChatReact;
var useCompletion = useCompletionReact;
var useAssistant = useAssistantReact;
var experimental_useObject = experimental_useObjectReact;
export {
  experimental_useObject,
  useAssistant,
  useChat,
  useCompletion
};
//# sourceMappingURL=index.mjs.map