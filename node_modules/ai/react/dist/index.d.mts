import * as _ai_sdk_react from '@ai-sdk/react';
import { useChat as useChat$1, useCompletion as useCompletion$1, useAssistant as useAssistant$1 } from '@ai-sdk/react';
export { CreateMessage, Message, UseChatHelpers, UseChatOptions } from '@ai-sdk/react';

/**
 * @deprecated Use `@ai-sdk/react` instead.
 */
declare const useChat: typeof useChat$1;
/**
 * @deprecated Use `@ai-sdk/react` instead.
 */
declare const useCompletion: typeof useCompletion$1;
/**
 * @deprecated Use `@ai-sdk/react` instead.
 */
declare const useAssistant: typeof useAssistant$1;
/**
 * @deprecated Use `@ai-sdk/react` instead.
 */
declare const experimental_useObject: <RESULT, INPUT = any>({ api, id, schema, initialValue, fetch, onError, onFinish, headers, credentials, }: _ai_sdk_react.Experimental_UseObjectOptions<RESULT>) => _ai_sdk_react.Experimental_UseObjectHelpers<RESULT, INPUT>;

export { experimental_useObject, useAssistant, useChat, useCompletion };
