{"version": 3, "sources": ["../index.ts"], "sourcesContent": ["import {\n  useChat as useChatReact,\n  useCompletion as useCompletionReact,\n  useAssistant as useAssistantReact,\n  experimental_useObject as experimental_useObjectReact,\n} from '@ai-sdk/react';\n\n/**\n * @deprecated Use `@ai-sdk/react` instead.\n */\nexport const useChat = useChatReact;\n\n/**\n * @deprecated Use `@ai-sdk/react` instead.\n */\nexport const useCompletion = useCompletionReact;\n\n/**\n * @deprecated Use `@ai-sdk/react` instead.\n */\nexport const useAssistant = useAssistantReact;\n\n/**\n * @deprecated Use `@ai-sdk/react` instead.\n */\nexport const experimental_useObject = experimental_useObjectReact;\n\nexport type {\n  /**\n   * @deprecated Use `@ai-sdk/react` instead.\n   */\n  CreateMessage,\n\n  /**\n   * @deprecated Use `@ai-sdk/react` instead.\n   */\n  Message,\n\n  /**\n   * @deprecated Use `@ai-sdk/react` instead.\n   */\n  UseChatOptions,\n\n  /**\n   * @deprecated Use `@ai-sdk/react` instead.\n   */\n  UseChatHelpers,\n} from '@ai-sdk/react';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAKO;AAKA,IAAM,UAAU,aAAAA;AAKhB,IAAM,gBAAgB,aAAAC;AAKtB,IAAM,eAAe,aAAAC;AAKrB,IAAM,yBAAyB,aAAAC;", "names": ["useChatReact", "useCompletionReact", "useAssistantReact", "experimental_useObjectReact"]}