{"version": 3, "sources": ["../ai-state.tsx", "../../util/create-resolvable-promise.ts", "../../util/is-function.ts", "../provider.tsx", "../stream-ui/stream-ui.tsx", "../../util/download-error.ts", "../../util/download.ts", "../../core/util/detect-mimetype.ts", "../../core/prompt/data-content.ts", "../../core/prompt/invalid-data-content-error.ts", "../../core/prompt/invalid-message-role-error.ts", "../../core/prompt/split-data-url.ts", "../../core/prompt/convert-to-language-model-prompt.ts", "../../errors/invalid-argument-error.ts", "../../core/prompt/prepare-call-settings.ts", "../../util/retry-with-exponential-backoff.ts", "../../util/retry-error.ts", "../../core/prompt/prepare-retries.ts", "../../core/prompt/prepare-tools-and-tool-choice.ts", "../../core/util/is-non-empty-object.ts", "../../core/prompt/standardize-prompt.ts", "../../core/prompt/attachments-to-parts.ts", "../../core/prompt/message-conversion-error.ts", "../../core/prompt/convert-to-core-messages.ts", "../../core/prompt/message.ts", "../../core/types/provider-metadata.ts", "../../core/types/json-value.ts", "../../core/prompt/content-part.ts", "../../core/prompt/tool-result-content.ts", "../../core/types/usage.ts", "../../errors/invalid-tool-arguments-error.ts", "../../errors/no-such-tool-error.ts", "../../util/is-async-generator.ts", "../../util/is-generator.ts", "../../util/constants.ts", "../streamable-ui/create-suspended-chunk.tsx", "../streamable-ui/create-streamable-ui.tsx", "../streamable-value/streamable-value.ts", "../streamable-value/create-streamable-value.ts"], "sourcesContent": ["import * as jsondiffpatch from 'jsondiffpatch';\nimport { AsyncLocalStorage } from 'node:async_hooks';\nimport { createResolvablePromise } from '../util/create-resolvable-promise';\nimport { isFunction } from '../util/is-function';\nimport type {\n  AIProvider,\n  InferAIState,\n  InternalAIStateStorageOptions,\n  MutableAIState,\n  ValueOrUpdater,\n} from './types';\n\n// It is possible that multiple AI requests get in concurrently, for different\n// AI instances. So ALS is necessary here for a simpler API.\nconst asyncAIStateStorage = new AsyncLocalStorage<{\n  currentState: any;\n  originalState: any;\n  sealed: boolean;\n  options: InternalAIStateStorageOptions;\n  mutationDeltaPromise?: Promise<any>;\n  mutationDeltaResolve?: (v: any) => void;\n}>();\n\nfunction getAIStateStoreOrThrow(message: string) {\n  const store = asyncAIStateStorage.getStore();\n  if (!store) {\n    throw new Error(message);\n  }\n  return store;\n}\n\nexport function withAIState<S, T>(\n  { state, options }: { state: S; options: InternalAIStateStorageOptions },\n  fn: () => T,\n): T {\n  return asyncAIStateStorage.run(\n    {\n      currentState: JSON.parse(JSON.stringify(state)), // deep clone object\n      originalState: state,\n      sealed: false,\n      options,\n    },\n    fn,\n  );\n}\n\nexport function getAIStateDeltaPromise() {\n  const store = getAIStateStoreOrThrow('Internal error occurred.');\n  return store.mutationDeltaPromise;\n}\n\n// Internal method. This will be called after the AI Action has been returned\n// and you can no longer call `getMutableAIState()` inside any async callbacks\n// created by that Action.\nexport function sealMutableAIState() {\n  const store = getAIStateStoreOrThrow('Internal error occurred.');\n  store.sealed = true;\n}\n\n/**\n * Get the current AI state.\n * If `key` is provided, it will return the value of the specified key in the\n * AI state, if it's an object. If it's not an object, it will throw an error.\n *\n * @example const state = getAIState() // Get the entire AI state\n * @example const field = getAIState('key') // Get the value of the key\n */\nfunction getAIState<AI extends AIProvider = any>(): Readonly<\n  InferAIState<AI, any>\n>;\nfunction getAIState<AI extends AIProvider = any>(\n  key: keyof InferAIState<AI, any>,\n): Readonly<InferAIState<AI, any>[typeof key]>;\nfunction getAIState<AI extends AIProvider = any>(\n  ...args: [] | [key: keyof InferAIState<AI, any>]\n) {\n  const store = getAIStateStoreOrThrow(\n    '`getAIState` must be called within an AI Action.',\n  );\n\n  if (args.length > 0) {\n    const key = args[0];\n    if (typeof store.currentState !== 'object') {\n      throw new Error(\n        `You can't get the \"${String(\n          key,\n        )}\" field from the AI state because it's not an object.`,\n      );\n    }\n    return store.currentState[key as keyof typeof store.currentState];\n  }\n\n  return store.currentState;\n}\n\n/**\n * Get the mutable AI state. Note that you must call `.done()` when finishing\n * updating the AI state.\n *\n * @example\n * ```tsx\n * const state = getMutableAIState()\n * state.update({ ...state.get(), key: 'value' })\n * state.update((currentState) => ({ ...currentState, key: 'value' }))\n * state.done()\n * ```\n *\n * @example\n * ```tsx\n * const state = getMutableAIState()\n * state.done({ ...state.get(), key: 'value' }) // Done with a new state\n * ```\n */\nfunction getMutableAIState<AI extends AIProvider = any>(): MutableAIState<\n  InferAIState<AI, any>\n>;\nfunction getMutableAIState<AI extends AIProvider = any>(\n  key: keyof InferAIState<AI, any>,\n): MutableAIState<InferAIState<AI, any>[typeof key]>;\nfunction getMutableAIState<AI extends AIProvider = any>(\n  ...args: [] | [key: keyof InferAIState<AI, any>]\n) {\n  type AIState = InferAIState<AI, any>;\n  type AIStateWithKey = typeof args extends [key: keyof AIState]\n    ? AIState[(typeof args)[0]]\n    : AIState;\n  type NewStateOrUpdater = ValueOrUpdater<AIStateWithKey>;\n\n  const store = getAIStateStoreOrThrow(\n    '`getMutableAIState` must be called within an AI Action.',\n  );\n\n  if (store.sealed) {\n    throw new Error(\n      \"`getMutableAIState` must be called before returning from an AI Action. Please move it to the top level of the Action's function body.\",\n    );\n  }\n\n  if (!store.mutationDeltaPromise) {\n    const { promise, resolve } = createResolvablePromise();\n    store.mutationDeltaPromise = promise;\n    store.mutationDeltaResolve = resolve;\n  }\n\n  function doUpdate(newState: NewStateOrUpdater, done: boolean) {\n    if (args.length > 0) {\n      if (typeof store.currentState !== 'object') {\n        const key = args[0];\n        throw new Error(\n          `You can't modify the \"${String(\n            key,\n          )}\" field of the AI state because it's not an object.`,\n        );\n      }\n    }\n\n    if (isFunction(newState)) {\n      if (args.length > 0) {\n        store.currentState[args[0]] = newState(store.currentState[args[0]]);\n      } else {\n        store.currentState = newState(store.currentState);\n      }\n    } else {\n      if (args.length > 0) {\n        store.currentState[args[0]] = newState;\n      } else {\n        store.currentState = newState;\n      }\n    }\n\n    store.options.onSetAIState?.({\n      key: args.length > 0 ? args[0] : undefined,\n      state: store.currentState,\n      done,\n    });\n  }\n\n  const mutableState = {\n    get: () => {\n      if (args.length > 0) {\n        const key = args[0];\n        if (typeof store.currentState !== 'object') {\n          throw new Error(\n            `You can't get the \"${String(\n              key,\n            )}\" field from the AI state because it's not an object.`,\n          );\n        }\n        return store.currentState[key] as Readonly<AIStateWithKey>;\n      }\n\n      return store.currentState as Readonly<AIState>;\n    },\n    update: function update(newAIState: NewStateOrUpdater) {\n      doUpdate(newAIState, false);\n    },\n    done: function done(...doneArgs: [] | [NewStateOrUpdater]) {\n      if (doneArgs.length > 0) {\n        doUpdate(doneArgs[0] as NewStateOrUpdater, true);\n      }\n\n      const delta = jsondiffpatch.diff(store.originalState, store.currentState);\n      store.mutationDeltaResolve!(delta);\n    },\n  };\n\n  return mutableState;\n}\n\nexport { getAIState, getMutableAIState };\n", "/**\n * Creates a Promise with externally accessible resolve and reject functions.\n *\n * @template T - The type of the value that the Promise will resolve to.\n * @returns An object containing:\n *   - promise: A Promise that can be resolved or rejected externally.\n *   - resolve: A function to resolve the Promise with a value of type T.\n *   - reject: A function to reject the Promise with an error.\n */\nexport function createResolvablePromise<T = any>(): {\n  promise: Promise<T>;\n  resolve: (value: T) => void;\n  reject: (error: unknown) => void;\n} {\n  let resolve: (value: T) => void;\n  let reject: (error: unknown) => void;\n\n  const promise = new Promise<T>((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n\n  return {\n    promise,\n    resolve: resolve!,\n    reject: reject!,\n  };\n}\n", "/**\n * Checks if the given value is a function.\n *\n * @param {unknown} value - The value to check.\n * @returns {boolean} True if the value is a function, false otherwise.\n */\nexport const isFunction = (value: unknown): value is Function =>\n  typeof value === 'function';\n", "// This file provides the AI context to all AI Actions via AsyncLocalStorage.\n\nimport * as React from 'react';\nimport { InternalAIProvider } from './rsc-shared.mjs';\nimport {\n  withAIState,\n  getAIStateDeltaPromise,\n  sealMutableAIState,\n} from './ai-state';\nimport type {\n  ServerWrappedActions,\n  AIAction,\n  AIActions,\n  AIProvider,\n  InternalAIStateStorageOptions,\n  OnSetAIState,\n  OnGetUIState,\n} from './types';\n\nasync function innerAction<T>(\n  {\n    action,\n    options,\n  }: { action: AIAction; options: InternalAIStateStorageOptions },\n  state: T,\n  ...args: unknown[]\n) {\n  'use server';\n  return await withAIState(\n    {\n      state,\n      options,\n    },\n    async () => {\n      const result = await action(...args);\n      sealMutableAIState();\n      return [getAIStateDeltaPromise() as Promise<T>, result];\n    },\n  );\n}\n\nfunction wrapAction<T = unknown>(\n  action: AIAction,\n  options: InternalAIStateStorageOptions,\n) {\n  return innerAction.bind(null, { action, options }) as AIAction<T>;\n}\n\nexport function createAI<\n  AIState = any,\n  UIState = any,\n  Actions extends AIActions = {},\n>({\n  actions,\n  initialAIState,\n  initialUIState,\n\n  onSetAIState,\n  onGetUIState,\n}: {\n  actions: Actions;\n  initialAIState?: AIState;\n  initialUIState?: UIState;\n\n  /**\n   * This function is called whenever the AI state is updated by an Action.\n   * You can use this to persist the AI state to a database, or to send it to a\n   * logging service.\n   */\n  onSetAIState?: OnSetAIState<AIState>;\n\n  /**\n   * This function is used to retrieve the UI state based on the AI state.\n   * For example, to render the initial UI state based on a given AI state, or\n   * to sync the UI state when the application is already loaded.\n   *\n   * If returning `undefined`, the client side UI state will not be updated.\n   *\n   * This function must be annotated with the `\"use server\"` directive.\n   *\n   * @example\n   * ```tsx\n   * onGetUIState: async () => {\n   *   'use server';\n   *\n   *   const currentAIState = getAIState();\n   *   const externalAIState = await loadAIStateFromDatabase();\n   *\n   *   if (currentAIState === externalAIState) return undefined;\n   *\n   *   // Update current AI state and return the new UI state\n   *   const state = getMutableAIState()\n   *   state.done(externalAIState)\n   *\n   *   return <div>...</div>;\n   * }\n   * ```\n   */\n  onGetUIState?: OnGetUIState<UIState>;\n}) {\n  // Wrap all actions with our HoC.\n  const wrappedActions: ServerWrappedActions = {};\n  for (const name in actions) {\n    wrappedActions[name] = wrapAction(actions[name], {\n      onSetAIState,\n    });\n  }\n\n  const wrappedSyncUIState = onGetUIState\n    ? wrapAction(onGetUIState, {})\n    : undefined;\n\n  const AI: AIProvider<AIState, UIState, Actions> = async props => {\n    if ('useState' in React) {\n      // This file must be running on the React Server layer.\n      // Ideally we should be using `import \"server-only\"` here but we can have a\n      // more customized error message with this implementation.\n      throw new Error(\n        'This component can only be used inside Server Components.',\n      );\n    }\n\n    let uiState = props.initialUIState ?? initialUIState;\n    let aiState = props.initialAIState ?? initialAIState;\n    let aiStateDelta = undefined;\n\n    if (wrappedSyncUIState) {\n      const [newAIStateDelta, newUIState] = await wrappedSyncUIState(aiState);\n      if (newUIState !== undefined) {\n        aiStateDelta = newAIStateDelta;\n        uiState = newUIState;\n      }\n    }\n\n    return (\n      <InternalAIProvider\n        wrappedActions={wrappedActions}\n        wrappedSyncUIState={wrappedSyncUIState}\n        initialUIState={uiState}\n        initialAIState={aiState}\n        initialAIStatePatch={aiStateDelta}\n      >\n        {props.children}\n      </InternalAIProvider>\n    );\n  };\n\n  return AI;\n}\n", "import { LanguageModelV1 } from '@ai-sdk/provider';\nimport { safeParseJSON } from '@ai-sdk/provider-utils';\nimport { ReactNode } from 'react';\nimport { z } from 'zod';\nimport { CallSettings } from '../../core/prompt/call-settings';\nimport { convertToLanguageModelPrompt } from '../../core/prompt/convert-to-language-model-prompt';\nimport { prepareCallSettings } from '../../core/prompt/prepare-call-settings';\nimport { prepareRetries } from '../../core/prompt/prepare-retries';\nimport { prepareToolsAndToolChoice } from '../../core/prompt/prepare-tools-and-tool-choice';\nimport { Prompt } from '../../core/prompt/prompt';\nimport { standardizePrompt } from '../../core/prompt/standardize-prompt';\nimport {\n  CallWarning,\n  FinishReason,\n  ProviderMetadata,\n  ToolChoice,\n} from '../../core/types';\nimport { ProviderOptions } from '../../core/types/provider-metadata';\nimport {\n  LanguageModelUsage,\n  calculateLanguageModelUsage,\n} from '../../core/types/usage';\nimport { InvalidToolArgumentsError } from '../../errors/invalid-tool-arguments-error';\nimport { NoSuchToolError } from '../../errors/no-such-tool-error';\nimport { createResolvablePromise } from '../../util/create-resolvable-promise';\nimport { isAsyncGenerator } from '../../util/is-async-generator';\nimport { isGenerator } from '../../util/is-generator';\nimport { createStreamableUI } from '../streamable-ui/create-streamable-ui';\n\ntype Streamable = ReactNode | Promise<ReactNode>;\n\ntype Renderer<T extends Array<any>> = (\n  ...args: T\n) =>\n  | Streamable\n  | Generator<Streamable, Streamable, void>\n  | AsyncGenerator<Streamable, Streamable, void>;\n\ntype RenderTool<PARAMETERS extends z.ZodTypeAny = any> = {\n  description?: string;\n  parameters: PARAMETERS;\n  generate?: Renderer<\n    [\n      z.infer<PARAMETERS>,\n      {\n        toolName: string;\n        toolCallId: string;\n      },\n    ]\n  >;\n};\n\ntype RenderText = Renderer<\n  [\n    {\n      /**\n       * The full text content from the model so far.\n       */\n      content: string;\n\n      /**\n       * The new appended text content from the model since the last `text` call.\n       */\n      delta: string;\n\n      /**\n       * Whether the model is done generating text.\n       * If `true`, the `content` will be the final output and this call will be the last.\n       */\n      done: boolean;\n    },\n  ]\n>;\n\ntype RenderResult = {\n  value: ReactNode;\n} & Awaited<ReturnType<LanguageModelV1['doStream']>>;\n\nconst defaultTextRenderer: RenderText = ({ content }: { content: string }) =>\n  content;\n\n/**\n * `streamUI` is a helper function to create a streamable UI from LLMs.\n */\nexport async function streamUI<\n  TOOLS extends { [name: string]: z.ZodTypeAny } = {},\n>({\n  model,\n  tools,\n  toolChoice,\n  system,\n  prompt,\n  messages,\n  maxRetries,\n  abortSignal,\n  headers,\n  initial,\n  text,\n  experimental_providerMetadata,\n  providerOptions = experimental_providerMetadata,\n  onFinish,\n  ...settings\n}: CallSettings &\n  Prompt & {\n    /**\n     * The language model to use.\n     */\n    model: LanguageModelV1;\n\n    /**\n     * The tools that the model can call. The model needs to support calling tools.\n     */\n    tools?: {\n      [name in keyof TOOLS]: RenderTool<TOOLS[name]>;\n    };\n\n    /**\n     * The tool choice strategy. Default: 'auto'.\n     */\n    toolChoice?: ToolChoice<TOOLS>;\n\n    text?: RenderText;\n    initial?: ReactNode;\n\n    /**\nAdditional provider-specific options. They are passed through\nto the provider from the AI SDK and enable provider-specific\nfunctionality that can be fully encapsulated in the provider.\n */\n    providerOptions?: ProviderOptions;\n\n    /**\n@deprecated Use `providerOptions` instead.\n*/\n    experimental_providerMetadata?: ProviderMetadata;\n\n    /**\n     * Callback that is called when the LLM response and the final object validation are finished.\n     */\n    onFinish?: (event: {\n      /**\n       * The reason why the generation finished.\n       */\n      finishReason: FinishReason;\n      /**\n       * The token usage of the generated response.\n       */\n      usage: LanguageModelUsage;\n      /**\n       * The final ui node that was generated.\n       */\n      value: ReactNode;\n      /**\n       * Warnings from the model provider (e.g. unsupported settings)\n       */\n      warnings?: CallWarning[];\n      /**\n       * Optional raw response data.\n       */\n      rawResponse?: {\n        /**\n         * Response headers.\n         */\n        headers?: Record<string, string>;\n      };\n    }) => Promise<void> | void;\n  }): Promise<RenderResult> {\n  // TODO: Remove these errors after the experimental phase.\n  if (typeof model === 'string') {\n    throw new Error(\n      '`model` cannot be a string in `streamUI`. Use the actual model instance instead.',\n    );\n  }\n  if ('functions' in settings) {\n    throw new Error(\n      '`functions` is not supported in `streamUI`, use `tools` instead.',\n    );\n  }\n  if ('provider' in settings) {\n    throw new Error(\n      '`provider` is no longer needed in `streamUI`. Use `model` instead.',\n    );\n  }\n  if (tools) {\n    for (const [name, tool] of Object.entries(tools)) {\n      if ('render' in tool) {\n        throw new Error(\n          'Tool definition in `streamUI` should not have `render` property. Use `generate` instead. Found in tool: ' +\n            name,\n        );\n      }\n    }\n  }\n\n  const ui = createStreamableUI(initial);\n\n  // The default text renderer just returns the content as string.\n  const textRender = text || defaultTextRenderer;\n\n  let finished: Promise<void> | undefined;\n\n  let finishEvent: {\n    finishReason: FinishReason;\n    usage: LanguageModelUsage;\n    warnings?: CallWarning[];\n    rawResponse?: {\n      headers?: Record<string, string>;\n    };\n  } | null = null;\n\n  async function render({\n    args,\n    renderer,\n    streamableUI,\n    isLastCall = false,\n  }: {\n    renderer: undefined | Renderer<any>;\n    args: [payload: any] | [payload: any, options: any];\n    streamableUI: ReturnType<typeof createStreamableUI>;\n    isLastCall?: boolean;\n  }) {\n    if (!renderer) return;\n\n    // create a promise that will be resolved when the render call is finished.\n    // it is appended to the `finished` promise chain to ensure the render call\n    // is finished before the next render call starts.\n    const renderFinished = createResolvablePromise<void>();\n    finished = finished\n      ? finished.then(() => renderFinished.promise)\n      : renderFinished.promise;\n\n    const rendererResult = renderer(...args);\n\n    if (isAsyncGenerator(rendererResult) || isGenerator(rendererResult)) {\n      while (true) {\n        const { done, value } = await rendererResult.next();\n        const node = await value;\n\n        if (isLastCall && done) {\n          streamableUI.done(node);\n        } else {\n          streamableUI.update(node);\n        }\n\n        if (done) break;\n      }\n    } else {\n      const node = await rendererResult;\n\n      if (isLastCall) {\n        streamableUI.done(node);\n      } else {\n        streamableUI.update(node);\n      }\n    }\n\n    // resolve the promise to signal that the render call is finished\n    renderFinished.resolve(undefined);\n  }\n\n  const { retry } = prepareRetries({ maxRetries });\n\n  const validatedPrompt = standardizePrompt({\n    prompt: { system, prompt, messages },\n    tools: undefined, // streamUI tools don't support multi-modal tool result conversion\n  });\n  const result = await retry(async () =>\n    model.doStream({\n      mode: {\n        type: 'regular',\n        ...prepareToolsAndToolChoice({\n          tools,\n          toolChoice,\n          activeTools: undefined,\n        }),\n      },\n      ...prepareCallSettings(settings),\n      inputFormat: validatedPrompt.type,\n      prompt: await convertToLanguageModelPrompt({\n        prompt: validatedPrompt,\n        modelSupportsImageUrls: model.supportsImageUrls,\n        modelSupportsUrl: model.supportsUrl?.bind(model), // support 'this' context\n      }),\n      providerMetadata: providerOptions,\n      abortSignal,\n      headers,\n    }),\n  );\n\n  // For the stream and consume it asynchronously:\n  const [stream, forkedStream] = result.stream.tee();\n  (async () => {\n    try {\n      let content = '';\n      let hasToolCall = false;\n\n      const reader = forkedStream.getReader();\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) break;\n\n        switch (value.type) {\n          case 'text-delta': {\n            content += value.textDelta;\n            render({\n              renderer: textRender,\n              args: [{ content, done: false, delta: value.textDelta }],\n              streamableUI: ui,\n            });\n            break;\n          }\n\n          case 'tool-call-delta': {\n            hasToolCall = true;\n            break;\n          }\n\n          case 'tool-call': {\n            const toolName = value.toolName as keyof TOOLS & string;\n\n            if (!tools) {\n              throw new NoSuchToolError({ toolName });\n            }\n\n            const tool = tools[toolName];\n            if (!tool) {\n              throw new NoSuchToolError({\n                toolName,\n                availableTools: Object.keys(tools),\n              });\n            }\n\n            hasToolCall = true;\n            const parseResult = safeParseJSON({\n              text: value.args,\n              schema: tool.parameters,\n            });\n\n            if (parseResult.success === false) {\n              throw new InvalidToolArgumentsError({\n                toolName,\n                toolArgs: value.args,\n                cause: parseResult.error,\n              });\n            }\n\n            render({\n              renderer: tool.generate,\n              args: [\n                parseResult.value,\n                {\n                  toolName,\n                  toolCallId: value.toolCallId,\n                },\n              ],\n              streamableUI: ui,\n              isLastCall: true,\n            });\n\n            break;\n          }\n\n          case 'error': {\n            throw value.error;\n          }\n\n          case 'finish': {\n            finishEvent = {\n              finishReason: value.finishReason,\n              usage: calculateLanguageModelUsage(value.usage),\n              warnings: result.warnings,\n              rawResponse: result.rawResponse,\n            };\n            break;\n          }\n        }\n      }\n\n      if (!hasToolCall) {\n        render({\n          renderer: textRender,\n          args: [{ content, done: true }],\n          streamableUI: ui,\n          isLastCall: true,\n        });\n      }\n\n      await finished;\n\n      if (finishEvent && onFinish) {\n        await onFinish({\n          ...finishEvent,\n          value: ui.value,\n        });\n      }\n    } catch (error) {\n      // During the stream rendering, we don't want to throw the error to the\n      // parent scope but only let the React's error boundary to catch it.\n      ui.error(error);\n    }\n  })();\n\n  return {\n    ...result,\n    stream,\n    value: ui.value,\n  };\n}\n", "import { AISDKError } from '@ai-sdk/provider';\n\nconst name = 'AI_DownloadError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class DownloadError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly url: string;\n  readonly statusCode?: number;\n  readonly statusText?: string;\n\n  constructor({\n    url,\n    statusCode,\n    statusText,\n    cause,\n    message = cause == null\n      ? `Failed to download ${url}: ${statusCode} ${statusText}`\n      : `Failed to download ${url}: ${cause}`,\n  }: {\n    url: string;\n    statusCode?: number;\n    statusText?: string;\n    message?: string;\n    cause?: unknown;\n  }) {\n    super({ name, message, cause });\n\n    this.url = url;\n    this.statusCode = statusCode;\n    this.statusText = statusText;\n  }\n\n  static isInstance(error: unknown): error is DownloadError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { DownloadError } from './download-error';\n\nexport async function download({ url }: { url: URL }): Promise<{\n  data: Uint8Array;\n  mimeType: string | undefined;\n}> {\n  const urlText = url.toString();\n  try {\n    const response = await fetch(urlText);\n\n    if (!response.ok) {\n      throw new DownloadError({\n        url: urlText,\n        statusCode: response.status,\n        statusText: response.statusText,\n      });\n    }\n\n    return {\n      data: new Uint8Array(await response.arrayBuffer()),\n      mimeType: response.headers.get('content-type') ?? undefined,\n    };\n  } catch (error) {\n    if (DownloadError.isInstance(error)) {\n      throw error;\n    }\n\n    throw new DownloadError({ url: urlText, cause: error });\n  }\n}\n", "import { convertBase64ToUint8Array } from '@ai-sdk/provider-utils';\n\nexport const imageMimeTypeSignatures = [\n  {\n    mimeType: 'image/gif' as const,\n    bytesPrefix: [0x47, 0x49, 0x46],\n    base64Prefix: 'R0lG',\n  },\n  {\n    mimeType: 'image/png' as const,\n    bytesPrefix: [0x89, 0x50, 0x4e, 0x47],\n    base64Prefix: 'iVBORw',\n  },\n  {\n    mimeType: 'image/jpeg' as const,\n    bytesPrefix: [0xff, 0xd8],\n    base64Prefix: '/9j/',\n  },\n  {\n    mimeType: 'image/webp' as const,\n    bytesPrefix: [0x52, 0x49, 0x46, 0x46],\n    base64Prefix: 'UklGRg',\n  },\n  {\n    mimeType: 'image/bmp' as const,\n    bytesPrefix: [0x42, 0x4d],\n    base64Prefix: 'Qk',\n  },\n  {\n    mimeType: 'image/tiff' as const,\n    bytesPrefix: [0x49, 0x49, 0x2a, 0x00],\n    base64Prefix: 'SUkqAA',\n  },\n  {\n    mimeType: 'image/tiff' as const,\n    bytesPrefix: [0x4d, 0x4d, 0x00, 0x2a],\n    base64Prefix: 'TU0AKg',\n  },\n  {\n    mimeType: 'image/avif' as const,\n    bytesPrefix: [\n      0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70, 0x61, 0x76, 0x69, 0x66,\n    ],\n    base64Prefix: 'AAAAIGZ0eXBhdmlm',\n  },\n  {\n    mimeType: 'image/heic' as const,\n    bytesPrefix: [\n      0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70, 0x68, 0x65, 0x69, 0x63,\n    ],\n    base64Prefix: 'AAAAIGZ0eXBoZWlj',\n  },\n] as const;\n\nexport const audioMimeTypeSignatures = [\n  {\n    mimeType: 'audio/mpeg' as const,\n    bytesPrefix: [0xff, 0xfb],\n    base64Prefix: '//s=',\n  },\n  {\n    mimeType: 'audio/wav' as const,\n    bytesPrefix: [0x52, 0x49, 0x46, 0x46],\n    base64Prefix: 'UklGR',\n  },\n  {\n    mimeType: 'audio/ogg' as const,\n    bytesPrefix: [0x4f, 0x67, 0x67, 0x53],\n    base64Prefix: 'T2dnUw',\n  },\n  {\n    mimeType: 'audio/flac' as const,\n    bytesPrefix: [0x66, 0x4c, 0x61, 0x43],\n    base64Prefix: 'ZkxhQw',\n  },\n  {\n    mimeType: 'audio/aac' as const,\n    bytesPrefix: [0x40, 0x15, 0x00, 0x00],\n    base64Prefix: 'QBUA',\n  },\n  {\n    mimeType: 'audio/mp4' as const,\n    bytesPrefix: [0x66, 0x74, 0x79, 0x70],\n    base64Prefix: 'ZnR5cA',\n  },\n] as const;\n\nconst stripID3 = (data: Uint8Array | string) => {\n  const bytes =\n    typeof data === 'string' ? convertBase64ToUint8Array(data) : data;\n  const id3Size =\n    ((bytes[6] & 0x7f) << 21) |\n    ((bytes[7] & 0x7f) << 14) |\n    ((bytes[8] & 0x7f) << 7) |\n    (bytes[9] & 0x7f);\n\n  // The raw MP3 starts here\n  return bytes.slice(id3Size + 10);\n};\n\nfunction stripID3TagsIfPresent(data: Uint8Array | string): Uint8Array | string {\n  const hasId3 =\n    (typeof data === 'string' && data.startsWith('SUQz')) ||\n    (typeof data !== 'string' &&\n      data.length > 10 &&\n      data[0] === 0x49 && // 'I'\n      data[1] === 0x44 && // 'D'\n      data[2] === 0x33); // '3'\n\n  return hasId3 ? stripID3(data) : data;\n}\n\nexport function detectMimeType({\n  data,\n  signatures,\n}: {\n  data: Uint8Array | string;\n  signatures: typeof audioMimeTypeSignatures | typeof imageMimeTypeSignatures;\n}): (typeof signatures)[number]['mimeType'] | undefined {\n  const processedData = stripID3TagsIfPresent(data);\n\n  for (const signature of signatures) {\n    if (\n      typeof processedData === 'string'\n        ? processedData.startsWith(signature.base64Prefix)\n        : processedData.length >= signature.bytesPrefix.length &&\n          signature.bytesPrefix.every(\n            (byte, index) => processedData[index] === byte,\n          )\n    ) {\n      return signature.mimeType;\n    }\n  }\n\n  return undefined;\n}\n", "import {\n  convertBase64ToUint8Array,\n  convertUint8ArrayToBase64,\n} from '@ai-sdk/provider-utils';\nimport { InvalidDataContentError } from './invalid-data-content-error';\nimport { z } from 'zod';\n\n/**\nData content. Can either be a base64-encoded string, a Uint8Array, an ArrayBuffer, or a Buffer.\n */\nexport type DataContent = string | Uint8Array | ArrayBuffer | Buffer;\n\n/**\n@internal\n */\nexport const dataContentSchema: z.ZodType<DataContent> = z.union([\n  z.string(),\n  z.instanceof(Uint8Array),\n  z.instanceof(ArrayBuffer),\n  z.custom(\n    // Buffer might not be available in some environments such as CloudFlare:\n    (value: unknown): value is Buffer =>\n      globalThis.Buffer?.isBuffer(value) ?? false,\n    { message: 'Must be a Buffer' },\n  ),\n]);\n\n/**\nConverts data content to a base64-encoded string.\n\n@param content - Data content to convert.\n@returns Base64-encoded string.\n*/\nexport function convertDataContentToBase64String(content: DataContent): string {\n  if (typeof content === 'string') {\n    return content;\n  }\n\n  if (content instanceof ArrayBuffer) {\n    return convertUint8ArrayToBase64(new Uint8Array(content));\n  }\n\n  return convertUint8ArrayToBase64(content);\n}\n\n/**\nConverts data content to a Uint8Array.\n\n@param content - Data content to convert.\n@returns Uint8Array.\n */\nexport function convertDataContentToUint8Array(\n  content: DataContent,\n): Uint8Array {\n  if (content instanceof Uint8Array) {\n    return content;\n  }\n\n  if (typeof content === 'string') {\n    try {\n      return convertBase64ToUint8Array(content);\n    } catch (error) {\n      throw new InvalidDataContentError({\n        message:\n          'Invalid data content. Content string is not a base64-encoded media.',\n        content,\n        cause: error,\n      });\n    }\n  }\n\n  if (content instanceof ArrayBuffer) {\n    return new Uint8Array(content);\n  }\n\n  throw new InvalidDataContentError({ content });\n}\n\n/**\n * Converts a Uint8Array to a string of text.\n *\n * @param uint8Array - The Uint8Array to convert.\n * @returns The converted string.\n */\nexport function convertUint8ArrayToText(uint8Array: Uint8Array): string {\n  try {\n    return new TextDecoder().decode(uint8Array);\n  } catch (error) {\n    throw new Error('Error decoding Uint8Array to text');\n  }\n}\n", "import { AISDKError } from '@ai-sdk/provider';\n\nconst name = 'AI_InvalidDataContentError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class InvalidDataContentError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly content: unknown;\n\n  constructor({\n    content,\n    cause,\n    message = `Invalid data content. Expected a base64 string, Uint8Array, ArrayBuffer, or Buffer, but got ${typeof content}.`,\n  }: {\n    content: unknown;\n    cause?: unknown;\n    message?: string;\n  }) {\n    super({ name, message, cause });\n\n    this.content = content;\n  }\n\n  static isInstance(error: unknown): error is InvalidDataContentError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from '@ai-sdk/provider';\n\nconst name = 'AI_InvalidMessageRoleError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class InvalidMessageRoleError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly role: string;\n\n  constructor({\n    role,\n    message = `Invalid message role: '${role}'. Must be one of: \"system\", \"user\", \"assistant\", \"tool\".`,\n  }: {\n    role: string;\n    message?: string;\n  }) {\n    super({ name, message });\n\n    this.role = role;\n  }\n\n  static isInstance(error: unknown): error is InvalidMessageRoleError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "export function splitDataUrl(dataUrl: string): {\n  mimeType: string | undefined;\n  base64Content: string | undefined;\n} {\n  try {\n    const [header, base64Content] = dataUrl.split(',');\n    return {\n      mimeType: header.split(';')[0].split(':')[1],\n      base64Content,\n    };\n  } catch (error) {\n    return {\n      mimeType: undefined,\n      base64Content: undefined,\n    };\n  }\n}\n", "import {\n  LanguageModelV1FilePart,\n  LanguageModelV1ImagePart,\n  LanguageModelV1Message,\n  LanguageModelV1Prompt,\n  LanguageModelV1TextPart,\n} from '@ai-sdk/provider';\nimport { download } from '../../util/download';\nimport { CoreMessage } from '../prompt/message';\nimport {\n  detectMimeType,\n  imageMimeTypeSignatures,\n} from '../util/detect-mimetype';\nimport { FilePart, ImagePart, TextPart } from './content-part';\nimport {\n  convertDataContentToBase64String,\n  convertDataContentToUint8Array,\n  DataContent,\n} from './data-content';\nimport { InvalidMessageRoleError } from './invalid-message-role-error';\nimport { splitDataUrl } from './split-data-url';\nimport { StandardizedPrompt } from './standardize-prompt';\n\nexport async function convertToLanguageModelPrompt({\n  prompt,\n  modelSupportsImageUrls = true,\n  modelSupportsUrl = () => false,\n  downloadImplementation = download,\n}: {\n  prompt: StandardizedPrompt;\n  modelSupportsImageUrls: boolean | undefined;\n  modelSupportsUrl: undefined | ((url: URL) => boolean);\n  downloadImplementation?: typeof download;\n}): Promise<LanguageModelV1Prompt> {\n  const downloadedAssets = await downloadAssets(\n    prompt.messages,\n    downloadImplementation,\n    modelSupportsImageUrls,\n    modelSupportsUrl,\n  );\n\n  return [\n    ...(prompt.system != null\n      ? [{ role: 'system' as const, content: prompt.system }]\n      : []),\n    ...prompt.messages.map(message =>\n      convertToLanguageModelMessage(message, downloadedAssets),\n    ),\n  ];\n}\n\n/**\n * Convert a CoreMessage to a LanguageModelV1Message.\n *\n * @param message The CoreMessage to convert.\n * @param downloadedAssets A map of URLs to their downloaded data. Only\n *   available if the model does not support URLs, null otherwise.\n */\nexport function convertToLanguageModelMessage(\n  message: CoreMessage,\n  downloadedAssets: Record<\n    string,\n    { mimeType: string | undefined; data: Uint8Array }\n  >,\n): LanguageModelV1Message {\n  const role = message.role;\n  switch (role) {\n    case 'system': {\n      return {\n        role: 'system',\n        content: message.content,\n        providerMetadata:\n          message.providerOptions ?? message.experimental_providerMetadata,\n      };\n    }\n\n    case 'user': {\n      if (typeof message.content === 'string') {\n        return {\n          role: 'user',\n          content: [{ type: 'text', text: message.content }],\n          providerMetadata:\n            message.providerOptions ?? message.experimental_providerMetadata,\n        };\n      }\n\n      return {\n        role: 'user',\n        content: message.content\n          .map(part => convertPartToLanguageModelPart(part, downloadedAssets))\n          // remove empty text parts:\n          .filter(part => part.type !== 'text' || part.text !== ''),\n        providerMetadata:\n          message.providerOptions ?? message.experimental_providerMetadata,\n      };\n    }\n\n    case 'assistant': {\n      if (typeof message.content === 'string') {\n        return {\n          role: 'assistant',\n          content: [{ type: 'text', text: message.content }],\n          providerMetadata:\n            message.providerOptions ?? message.experimental_providerMetadata,\n        };\n      }\n\n      return {\n        role: 'assistant',\n        content: message.content\n          .filter(\n            // remove empty text parts:\n            part => part.type !== 'text' || part.text !== '',\n          )\n          .map(part => {\n            const providerOptions =\n              part.providerOptions ?? part.experimental_providerMetadata;\n\n            switch (part.type) {\n              case 'file': {\n                return {\n                  type: 'file',\n                  data:\n                    part.data instanceof URL\n                      ? part.data\n                      : convertDataContentToBase64String(part.data),\n                  filename: part.filename,\n                  mimeType: part.mimeType,\n                  providerMetadata: providerOptions,\n                };\n              }\n              case 'reasoning': {\n                return {\n                  type: 'reasoning',\n                  text: part.text,\n                  signature: part.signature,\n                  providerMetadata: providerOptions,\n                };\n              }\n              case 'redacted-reasoning': {\n                return {\n                  type: 'redacted-reasoning',\n                  data: part.data,\n                  providerMetadata: providerOptions,\n                };\n              }\n              case 'text': {\n                return {\n                  type: 'text' as const,\n                  text: part.text,\n                  providerMetadata: providerOptions,\n                };\n              }\n              case 'tool-call': {\n                return {\n                  type: 'tool-call' as const,\n                  toolCallId: part.toolCallId,\n                  toolName: part.toolName,\n                  args: part.args,\n                  providerMetadata: providerOptions,\n                };\n              }\n            }\n          }),\n        providerMetadata:\n          message.providerOptions ?? message.experimental_providerMetadata,\n      };\n    }\n\n    case 'tool': {\n      return {\n        role: 'tool',\n        content: message.content.map(part => ({\n          type: 'tool-result',\n          toolCallId: part.toolCallId,\n          toolName: part.toolName,\n          result: part.result,\n          content: part.experimental_content,\n          isError: part.isError,\n          providerMetadata:\n            part.providerOptions ?? part.experimental_providerMetadata,\n        })),\n        providerMetadata:\n          message.providerOptions ?? message.experimental_providerMetadata,\n      };\n    }\n\n    default: {\n      const _exhaustiveCheck: never = role;\n      throw new InvalidMessageRoleError({ role: _exhaustiveCheck });\n    }\n  }\n}\n\n/**\n * Downloads images and files from URLs in the messages.\n */\nasync function downloadAssets(\n  messages: CoreMessage[],\n  downloadImplementation: typeof download,\n  modelSupportsImageUrls: boolean | undefined,\n  modelSupportsUrl: (url: URL) => boolean,\n): Promise<Record<string, { mimeType: string | undefined; data: Uint8Array }>> {\n  const urls = messages\n    .filter(message => message.role === 'user')\n    .map(message => message.content)\n    .filter((content): content is Array<TextPart | ImagePart | FilePart> =>\n      Array.isArray(content),\n    )\n    .flat()\n    .filter(\n      (part): part is ImagePart | FilePart =>\n        part.type === 'image' || part.type === 'file',\n    )\n    /**\n     * Filter out image parts if the model supports image URLs, before letting it\n     * decide if it supports a particular URL.\n     */\n    .filter(\n      (part): part is ImagePart | FilePart =>\n        !(part.type === 'image' && modelSupportsImageUrls === true),\n    )\n    .map(part => (part.type === 'image' ? part.image : part.data))\n    .map(part =>\n      // support string urls:\n      typeof part === 'string' &&\n      (part.startsWith('http:') || part.startsWith('https:'))\n        ? new URL(part)\n        : part,\n    )\n    .filter((image): image is URL => image instanceof URL)\n    /**\n     * Filter out URLs that the model supports natively, so we don't download them.\n     */\n    .filter(url => !modelSupportsUrl(url));\n\n  // download in parallel:\n  const downloadedImages = await Promise.all(\n    urls.map(async url => ({\n      url,\n      data: await downloadImplementation({ url }),\n    })),\n  );\n\n  return Object.fromEntries(\n    downloadedImages.map(({ url, data }) => [url.toString(), data]),\n  );\n}\n\n/**\n * Convert part of a message to a LanguageModelV1Part.\n * @param part The part to convert.\n * @param downloadedAssets A map of URLs to their downloaded data. Only\n *  available if the model does not support URLs, null otherwise.\n *\n * @returns The converted part.\n */\nfunction convertPartToLanguageModelPart(\n  part: TextPart | ImagePart | FilePart,\n  downloadedAssets: Record<\n    string,\n    { mimeType: string | undefined; data: Uint8Array }\n  >,\n):\n  | LanguageModelV1TextPart\n  | LanguageModelV1ImagePart\n  | LanguageModelV1FilePart {\n  if (part.type === 'text') {\n    return {\n      type: 'text',\n      text: part.text,\n      providerMetadata:\n        part.providerOptions ?? part.experimental_providerMetadata,\n    };\n  }\n\n  let mimeType: string | undefined = part.mimeType;\n  let data: DataContent | URL;\n  let content: URL | ArrayBuffer | string;\n  let normalizedData: Uint8Array | URL;\n\n  const type = part.type;\n  switch (type) {\n    case 'image':\n      data = part.image;\n      break;\n    case 'file':\n      data = part.data;\n      break;\n    default:\n      throw new Error(`Unsupported part type: ${type}`);\n  }\n\n  // Attempt to create a URL from the data. If it fails, we can assume the data\n  // is not a URL and likely some other sort of data.\n  try {\n    content = typeof data === 'string' ? new URL(data) : data;\n  } catch (error) {\n    content = data;\n  }\n\n  // If we successfully created a URL, we can use that to normalize the data\n  // either by passing it through or converting normalizing the base64 content\n  // to a Uint8Array.\n  if (content instanceof URL) {\n    // If the content is a data URL, we want to convert that to a Uint8Array\n    if (content.protocol === 'data:') {\n      const { mimeType: dataUrlMimeType, base64Content } = splitDataUrl(\n        content.toString(),\n      );\n\n      if (dataUrlMimeType == null || base64Content == null) {\n        throw new Error(`Invalid data URL format in part ${type}`);\n      }\n\n      mimeType = dataUrlMimeType;\n      normalizedData = convertDataContentToUint8Array(base64Content);\n    } else {\n      /**\n       * If the content is a URL, we should first see if it was downloaded. And if not,\n       * we can let the model decide if it wants to support the URL. This also allows\n       * for non-HTTP URLs to be passed through (e.g. gs://).\n       */\n      const downloadedFile = downloadedAssets[content.toString()];\n      if (downloadedFile) {\n        normalizedData = downloadedFile.data;\n        mimeType ??= downloadedFile.mimeType;\n      } else {\n        normalizedData = content;\n      }\n    }\n  } else {\n    // Since we know now the content is not a URL, we can attempt to normalize\n    // the data assuming it is some sort of data.\n    normalizedData = convertDataContentToUint8Array(content);\n  }\n\n  // Now that we have the normalized data either as a URL or a Uint8Array,\n  // we can create the LanguageModelV1Part.\n  switch (type) {\n    case 'image': {\n      // When possible, try to detect the mimetype automatically\n      // to deal with incorrect mimetype inputs.\n      // When detection fails, use provided mimetype.\n\n      if (normalizedData instanceof Uint8Array) {\n        mimeType =\n          detectMimeType({\n            data: normalizedData,\n            signatures: imageMimeTypeSignatures,\n          }) ?? mimeType;\n      }\n      return {\n        type: 'image',\n        image: normalizedData,\n        mimeType,\n        providerMetadata:\n          part.providerOptions ?? part.experimental_providerMetadata,\n      };\n    }\n\n    case 'file': {\n      // We should have a mimeType at this point, if not, throw an error.\n      if (mimeType == null) {\n        throw new Error(`Mime type is missing for file part`);\n      }\n\n      return {\n        type: 'file',\n        data:\n          normalizedData instanceof Uint8Array\n            ? convertDataContentToBase64String(normalizedData)\n            : normalizedData,\n        filename: part.filename,\n        mimeType,\n        providerMetadata:\n          part.providerOptions ?? part.experimental_providerMetadata,\n      };\n    }\n  }\n}\n", "import { AISDKError } from '@ai-sdk/provider';\n\nconst name = 'AI_InvalidArgumentError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class InvalidArgumentError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly parameter: string;\n  readonly value: unknown;\n\n  constructor({\n    parameter,\n    value,\n    message,\n  }: {\n    parameter: string;\n    value: unknown;\n    message: string;\n  }) {\n    super({\n      name,\n      message: `Invalid argument for parameter ${parameter}: ${message}`,\n    });\n\n    this.parameter = parameter;\n    this.value = value;\n  }\n\n  static isInstance(error: unknown): error is InvalidArgumentError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { InvalidArgumentError } from '../../errors/invalid-argument-error';\nimport { CallSettings } from './call-settings';\n\n/**\n * Validates call settings and sets default values.\n */\nexport function prepareCallSettings({\n  maxTokens,\n  temperature,\n  topP,\n  topK,\n  presencePenalty,\n  frequencyPenalty,\n  stopSequences,\n  seed,\n}: Omit<CallSettings, 'abortSignal' | 'headers' | 'maxRetries'>): Omit<\n  CallSettings,\n  'abortSignal' | 'headers' | 'maxRetries'\n> {\n  if (maxTokens != null) {\n    if (!Number.isInteger(maxTokens)) {\n      throw new InvalidArgumentError({\n        parameter: 'maxTokens',\n        value: maxTokens,\n        message: 'maxTokens must be an integer',\n      });\n    }\n\n    if (maxTokens < 1) {\n      throw new InvalidArgumentError({\n        parameter: 'maxTokens',\n        value: maxTokens,\n        message: 'maxTokens must be >= 1',\n      });\n    }\n  }\n\n  if (temperature != null) {\n    if (typeof temperature !== 'number') {\n      throw new InvalidArgumentError({\n        parameter: 'temperature',\n        value: temperature,\n        message: 'temperature must be a number',\n      });\n    }\n  }\n\n  if (topP != null) {\n    if (typeof topP !== 'number') {\n      throw new InvalidArgumentError({\n        parameter: 'topP',\n        value: topP,\n        message: 'topP must be a number',\n      });\n    }\n  }\n\n  if (topK != null) {\n    if (typeof topK !== 'number') {\n      throw new InvalidArgumentError({\n        parameter: 'topK',\n        value: topK,\n        message: 'topK must be a number',\n      });\n    }\n  }\n\n  if (presencePenalty != null) {\n    if (typeof presencePenalty !== 'number') {\n      throw new InvalidArgumentError({\n        parameter: 'presencePenalty',\n        value: presencePenalty,\n        message: 'presencePenalty must be a number',\n      });\n    }\n  }\n\n  if (frequencyPenalty != null) {\n    if (typeof frequencyPenalty !== 'number') {\n      throw new InvalidArgumentError({\n        parameter: 'frequencyPenalty',\n        value: frequencyPenalty,\n        message: 'frequencyPenalty must be a number',\n      });\n    }\n  }\n\n  if (seed != null) {\n    if (!Number.isInteger(seed)) {\n      throw new InvalidArgumentError({\n        parameter: 'seed',\n        value: seed,\n        message: 'seed must be an integer',\n      });\n    }\n  }\n\n  return {\n    maxTokens,\n    // TODO v5 remove default 0 for temperature\n    temperature: temperature ?? 0,\n    topP,\n    topK,\n    presencePenalty,\n    frequencyPenalty,\n    stopSequences:\n      stopSequences != null && stopSequences.length > 0\n        ? stopSequences\n        : undefined,\n    seed,\n  };\n}\n", "import { APICallError } from '@ai-sdk/provider';\nimport { delay, getErrorMessage, isAbortError } from '@ai-sdk/provider-utils';\nimport { RetryError } from './retry-error';\n\nexport type RetryFunction = <OUTPUT>(\n  fn: () => PromiseLike<OUTPUT>,\n) => PromiseLike<OUTPUT>;\n\n/**\nThe `retryWithExponentialBackoff` strategy retries a failed API call with an exponential backoff.\nYou can configure the maximum number of retries, the initial delay, and the backoff factor.\n */\nexport const retryWithExponentialBackoff =\n  ({\n    maxRetries = 2,\n    initialDelayInMs = 2000,\n    backoffFactor = 2,\n  } = {}): RetryFunction =>\n  async <OUTPUT>(f: () => PromiseLike<OUTPUT>) =>\n    _retryWithExponentialBackoff(f, {\n      maxRetries,\n      delayInMs: initialDelayInMs,\n      backoffFactor,\n    });\n\nasync function _retryWithExponentialBackoff<OUTPUT>(\n  f: () => PromiseLike<OUTPUT>,\n  {\n    maxRetries,\n    delayInMs,\n    backoffFactor,\n  }: { maxRetries: number; delayInMs: number; backoffFactor: number },\n  errors: unknown[] = [],\n): Promise<OUTPUT> {\n  try {\n    return await f();\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error; // don't retry when the request was aborted\n    }\n\n    if (maxRetries === 0) {\n      throw error; // don't wrap the error when retries are disabled\n    }\n\n    const errorMessage = getErrorMessage(error);\n    const newErrors = [...errors, error];\n    const tryNumber = newErrors.length;\n\n    if (tryNumber > maxRetries) {\n      throw new RetryError({\n        message: `Failed after ${tryNumber} attempts. Last error: ${errorMessage}`,\n        reason: 'maxRetriesExceeded',\n        errors: newErrors,\n      });\n    }\n\n    if (\n      error instanceof Error &&\n      APICallError.isInstance(error) &&\n      error.isRetryable === true &&\n      tryNumber <= maxRetries\n    ) {\n      await delay(delayInMs);\n      return _retryWithExponentialBackoff(\n        f,\n        { maxRetries, delayInMs: backoffFactor * delayInMs, backoffFactor },\n        newErrors,\n      );\n    }\n\n    if (tryNumber === 1) {\n      throw error; // don't wrap the error when a non-retryable error occurs on the first try\n    }\n\n    throw new RetryError({\n      message: `Failed after ${tryNumber} attempts with non-retryable error: '${errorMessage}'`,\n      reason: 'errorNotRetryable',\n      errors: newErrors,\n    });\n  }\n}\n", "import { AISDKError } from '@ai-sdk/provider';\n\nconst name = 'AI_RetryError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport type RetryErrorReason =\n  | 'maxRetriesExceeded'\n  | 'errorNotRetryable'\n  | 'abort';\n\nexport class RetryError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  // note: property order determines debugging output\n  readonly reason: RetryErrorReason;\n  readonly lastError: unknown;\n  readonly errors: Array<unknown>;\n\n  constructor({\n    message,\n    reason,\n    errors,\n  }: {\n    message: string;\n    reason: RetryErrorReason;\n    errors: Array<unknown>;\n  }) {\n    super({ name, message });\n\n    this.reason = reason;\n    this.errors = errors;\n\n    // separate our last error to make debugging via log easier:\n    this.lastError = errors[errors.length - 1];\n  }\n\n  static isInstance(error: unknown): error is RetryError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { InvalidArgumentError } from '../../errors/invalid-argument-error';\nimport {\n  RetryFunction,\n  retryWithExponentialBackoff,\n} from '../../util/retry-with-exponential-backoff';\n\n/**\n * Validate and prepare retries.\n */\nexport function prepareRetries({\n  maxRetries,\n}: {\n  maxRetries: number | undefined;\n}): {\n  maxRetries: number;\n  retry: RetryFunction;\n} {\n  if (maxRetries != null) {\n    if (!Number.isInteger(maxRetries)) {\n      throw new InvalidArgumentError({\n        parameter: 'maxRetries',\n        value: maxRetries,\n        message: 'maxRetries must be an integer',\n      });\n    }\n\n    if (maxRetries < 0) {\n      throw new InvalidArgumentError({\n        parameter: 'maxRetries',\n        value: maxRetries,\n        message: 'maxRetries must be >= 0',\n      });\n    }\n  }\n\n  const maxRetriesResult = maxRetries ?? 2;\n\n  return {\n    maxRetries: maxRetriesResult,\n    retry: retryWithExponentialBackoff({ maxRetries: maxRetriesResult }),\n  };\n}\n", "import {\n  LanguageModelV1FunctionTool,\n  LanguageModelV1ProviderDefinedTool,\n  LanguageModelV1ToolChoice,\n} from '@ai-sdk/provider';\nimport { asSchema } from '@ai-sdk/ui-utils';\nimport { ToolSet } from '../generate-text';\nimport { ToolChoice } from '../types/language-model';\nimport { isNonEmptyObject } from '../util/is-non-empty-object';\n\nexport function prepareToolsAndToolChoice<TOOLS extends ToolSet>({\n  tools,\n  toolChoice,\n  activeTools,\n}: {\n  tools: TOOLS | undefined;\n  toolChoice: ToolChoice<TOOLS> | undefined;\n  activeTools: Array<keyof TOOLS> | undefined;\n}): {\n  tools:\n    | Array<LanguageModelV1FunctionTool | LanguageModelV1ProviderDefinedTool>\n    | undefined;\n  toolChoice: LanguageModelV1ToolChoice | undefined;\n} {\n  if (!isNonEmptyObject(tools)) {\n    return {\n      tools: undefined,\n      toolChoice: undefined,\n    };\n  }\n\n  // when activeTools is provided, we only include the tools that are in the list:\n  const filteredTools =\n    activeTools != null\n      ? Object.entries(tools).filter(([name]) =>\n          activeTools.includes(name as keyof TOOLS),\n        )\n      : Object.entries(tools);\n\n  return {\n    tools: filteredTools.map(([name, tool]) => {\n      const toolType = tool.type;\n      switch (toolType) {\n        case undefined:\n        case 'function':\n          return {\n            type: 'function' as const,\n            name,\n            description: tool.description,\n            parameters: asSchema(tool.parameters).jsonSchema,\n          };\n        case 'provider-defined':\n          return {\n            type: 'provider-defined' as const,\n            name,\n            id: tool.id,\n            args: tool.args,\n          };\n        default: {\n          const exhaustiveCheck: never = toolType;\n          throw new Error(`Unsupported tool type: ${exhaustiveCheck}`);\n        }\n      }\n    }),\n    toolChoice:\n      toolChoice == null\n        ? { type: 'auto' }\n        : typeof toolChoice === 'string'\n          ? { type: toolChoice }\n          : { type: 'tool' as const, toolName: toolChoice.toolName as string },\n  };\n}\n", "export function isNonEmptyObject(\n  object: Record<string, unknown> | undefined | null,\n): object is Record<string, unknown> {\n  return object != null && Object.keys(object).length > 0;\n}\n", "import { InvalidPromptError } from '@ai-sdk/provider';\nimport { safeValidateTypes } from '@ai-sdk/provider-utils';\nimport { Message } from '@ai-sdk/ui-utils';\nimport { z } from 'zod';\nimport { ToolSet } from '../generate-text/tool-set';\nimport { convertToCoreMessages } from './convert-to-core-messages';\nimport { CoreMessage, coreMessageSchema } from './message';\nimport { Prompt } from './prompt';\n\nexport type StandardizedPrompt = {\n  /**\n   * Original prompt type. This is forwarded to the providers and can be used\n   * to write send raw text to providers that support it.\n   */\n  type: 'prompt' | 'messages';\n\n  /**\n   * System message.\n   */\n  system?: string;\n\n  /**\n   * Messages.\n   */\n  messages: CoreMessage[];\n};\n\nexport function standardizePrompt<TOOLS extends ToolSet>({\n  prompt,\n  tools,\n}: {\n  prompt: Prompt;\n  tools: undefined | TOOLS;\n}): StandardizedPrompt {\n  if (prompt.prompt == null && prompt.messages == null) {\n    throw new InvalidPromptError({\n      prompt,\n      message: 'prompt or messages must be defined',\n    });\n  }\n\n  if (prompt.prompt != null && prompt.messages != null) {\n    throw new InvalidPromptError({\n      prompt,\n      message: 'prompt and messages cannot be defined at the same time',\n    });\n  }\n\n  // validate that system is a string\n  if (prompt.system != null && typeof prompt.system !== 'string') {\n    throw new InvalidPromptError({\n      prompt,\n      message: 'system must be a string',\n    });\n  }\n\n  // type: prompt\n  if (prompt.prompt != null) {\n    // validate that prompt is a string\n    if (typeof prompt.prompt !== 'string') {\n      throw new InvalidPromptError({\n        prompt,\n        message: 'prompt must be a string',\n      });\n    }\n\n    return {\n      type: 'prompt',\n      system: prompt.system,\n      messages: [\n        {\n          role: 'user',\n          content: prompt.prompt,\n        },\n      ],\n    };\n  }\n\n  // type: messages\n  if (prompt.messages != null) {\n    const promptType = detectPromptType(prompt.messages);\n\n    const messages: CoreMessage[] =\n      promptType === 'ui-messages'\n        ? convertToCoreMessages(prompt.messages as Omit<Message, 'id'>[], {\n            tools,\n          })\n        : (prompt.messages as CoreMessage[]);\n\n    if (messages.length === 0) {\n      throw new InvalidPromptError({\n        prompt,\n        message: 'messages must not be empty',\n      });\n    }\n\n    const validationResult = safeValidateTypes({\n      value: messages,\n      schema: z.array(coreMessageSchema),\n    });\n\n    if (!validationResult.success) {\n      throw new InvalidPromptError({\n        prompt,\n        message: [\n          'message must be a CoreMessage or a UI message',\n          `Validation error: ${validationResult.error.message}`,\n        ].join('\\n'),\n        cause: validationResult.error,\n      });\n    }\n\n    return {\n      type: 'messages',\n      messages,\n      system: prompt.system,\n    };\n  }\n\n  throw new Error('unreachable');\n}\n\nfunction detectPromptType(\n  prompt: Array<any>,\n): 'ui-messages' | 'messages' | 'other' {\n  if (!Array.isArray(prompt)) {\n    throw new InvalidPromptError({\n      prompt,\n      message: [\n        'messages must be an array of CoreMessage or UIMessage',\n        `Received non-array value: ${JSON.stringify(prompt)}`,\n      ].join('\\n'),\n      cause: prompt,\n    });\n  }\n\n  if (prompt.length === 0) {\n    return 'messages';\n  }\n\n  const characteristics = prompt.map(detectSingleMessageCharacteristics);\n\n  if (characteristics.some(c => c === 'has-ui-specific-parts')) {\n    return 'ui-messages';\n  }\n\n  const nonMessageIndex = characteristics.findIndex(\n    c => c !== 'has-core-specific-parts' && c !== 'message',\n  );\n\n  if (nonMessageIndex === -1) {\n    return 'messages';\n  }\n\n  throw new InvalidPromptError({\n    prompt,\n    message: [\n      'messages must be an array of CoreMessage or UIMessage',\n      `Received message of type: \"${characteristics[nonMessageIndex]}\" at index ${nonMessageIndex}`,\n      `messages[${nonMessageIndex}]: ${JSON.stringify(prompt[nonMessageIndex])}`,\n    ].join('\\n'),\n    cause: prompt,\n  });\n}\n\nfunction detectSingleMessageCharacteristics(\n  message: any,\n): 'has-ui-specific-parts' | 'has-core-specific-parts' | 'message' | 'other' {\n  if (\n    typeof message === 'object' &&\n    message !== null &&\n    (message.role === 'function' || // UI-only role\n      message.role === 'data' || // UI-only role\n      'toolInvocations' in message || // UI-specific field\n      'parts' in message || // UI-specific field\n      'experimental_attachments' in message)\n  ) {\n    return 'has-ui-specific-parts';\n  } else if (\n    typeof message === 'object' &&\n    message !== null &&\n    'content' in message &&\n    (Array.isArray(message.content) || // Core messages can have array content\n      'experimental_providerMetadata' in message ||\n      'providerOptions' in message)\n  ) {\n    return 'has-core-specific-parts';\n  } else if (\n    typeof message === 'object' &&\n    message !== null &&\n    'role' in message &&\n    'content' in message &&\n    typeof message.content === 'string' &&\n    ['system', 'user', 'assistant', 'tool'].includes(message.role)\n  ) {\n    return 'message';\n  } else {\n    return 'other';\n  }\n}\n", "import { Attachment } from '@ai-sdk/ui-utils';\nimport { FilePart, ImagePart, TextPart } from './content-part';\nimport {\n  convertDataContentToUint8Array,\n  convertUint8ArrayToText,\n} from './data-content';\n\ntype ContentPart = TextPart | ImagePart | FilePart;\n\n/**\n * Converts a list of attachments to a list of content parts\n * for consumption by `ai/core` functions.\n * Currently only supports images and text attachments.\n */\nexport function attachmentsToParts(attachments: Attachment[]): ContentPart[] {\n  const parts: ContentPart[] = [];\n\n  for (const attachment of attachments) {\n    let url;\n\n    try {\n      url = new URL(attachment.url);\n    } catch (error) {\n      throw new Error(`Invalid URL: ${attachment.url}`);\n    }\n\n    switch (url.protocol) {\n      case 'http:':\n      case 'https:': {\n        if (attachment.contentType?.startsWith('image/')) {\n          parts.push({ type: 'image', image: url });\n        } else {\n          if (!attachment.contentType) {\n            throw new Error(\n              'If the attachment is not an image, it must specify a content type',\n            );\n          }\n\n          parts.push({\n            type: 'file',\n            data: url,\n            mimeType: attachment.contentType,\n          });\n        }\n        break;\n      }\n\n      case 'data:': {\n        let header;\n        let base64Content;\n        let mimeType;\n\n        try {\n          [header, base64Content] = attachment.url.split(',');\n          mimeType = header.split(';')[0].split(':')[1];\n        } catch (error) {\n          throw new Error(`Error processing data URL: ${attachment.url}`);\n        }\n\n        if (mimeType == null || base64Content == null) {\n          throw new Error(`Invalid data URL format: ${attachment.url}`);\n        }\n\n        if (attachment.contentType?.startsWith('image/')) {\n          parts.push({\n            type: 'image',\n            image: convertDataContentToUint8Array(base64Content),\n          });\n        } else if (attachment.contentType?.startsWith('text/')) {\n          parts.push({\n            type: 'text',\n            text: convertUint8ArrayToText(\n              convertDataContentToUint8Array(base64Content),\n            ),\n          });\n        } else {\n          if (!attachment.contentType) {\n            throw new Error(\n              'If the attachment is not an image or text, it must specify a content type',\n            );\n          }\n\n          parts.push({\n            type: 'file',\n            data: base64Content,\n            mimeType: attachment.contentType,\n          });\n        }\n\n        break;\n      }\n\n      default: {\n        throw new Error(`Unsupported URL protocol: ${url.protocol}`);\n      }\n    }\n  }\n\n  return parts;\n}\n", "import { AISDKError } from '@ai-sdk/provider';\nimport { Message } from '@ai-sdk/ui-utils';\n\nconst name = 'AI_MessageConversionError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class MessageConversionError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly originalMessage: Omit<Message, 'id'>;\n\n  constructor({\n    originalMessage,\n    message,\n  }: {\n    originalMessage: Omit<Message, 'id'>;\n    message: string;\n  }) {\n    super({ name, message });\n\n    this.originalMessage = originalMessage;\n  }\n\n  static isInstance(error: unknown): error is MessageConversionError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import {\n  FileUIPart,\n  Message,\n  <PERSON>ingUIPart,\n  <PERSON>UIPart,\n  ToolInvocationUIPart,\n} from '@ai-sdk/ui-utils';\nimport { ToolSet } from '../generate-text/tool-set';\nimport {\n  AssistantContent,\n  CoreMessage,\n  ToolCallPart,\n  ToolResultPart,\n} from '../prompt';\nimport { attachmentsToParts } from './attachments-to-parts';\nimport { MessageConversionError } from './message-conversion-error';\n\n/**\nConverts an array of messages from useChat into an array of CoreMessages that can be used\nwith the AI core functions (e.g. `streamText`).\n */\nexport function convertToCoreMessages<TOOLS extends ToolSet = never>(\n  messages: Array<Omit<Message, 'id'>>,\n  options?: { tools?: TOOLS },\n) {\n  const tools = options?.tools ?? ({} as TOOLS);\n  const coreMessages: CoreMessage[] = [];\n\n  for (let i = 0; i < messages.length; i++) {\n    const message = messages[i];\n    const isLastMessage = i === messages.length - 1;\n    const { role, content, experimental_attachments } = message;\n\n    switch (role) {\n      case 'system': {\n        coreMessages.push({\n          role: 'system',\n          content,\n        });\n        break;\n      }\n\n      case 'user': {\n        if (message.parts == null) {\n          coreMessages.push({\n            role: 'user',\n            content: experimental_attachments\n              ? [\n                  { type: 'text', text: content },\n                  ...attachmentsToParts(experimental_attachments),\n                ]\n              : content,\n          });\n        } else {\n          const textParts = message.parts\n            .filter(part => part.type === 'text')\n            .map(part => ({\n              type: 'text' as const,\n              text: part.text,\n            }));\n\n          coreMessages.push({\n            role: 'user',\n            content: experimental_attachments\n              ? [...textParts, ...attachmentsToParts(experimental_attachments)]\n              : textParts,\n          });\n        }\n        break;\n      }\n\n      case 'assistant': {\n        if (message.parts != null) {\n          let currentStep = 0;\n          let blockHasToolInvocations = false;\n          let block: Array<\n            TextUIPart | ToolInvocationUIPart | ReasoningUIPart | FileUIPart\n          > = [];\n\n          function processBlock() {\n            const content: AssistantContent = [];\n\n            for (const part of block) {\n              switch (part.type) {\n                case 'file':\n                case 'text': {\n                  content.push(part);\n                  break;\n                }\n                case 'reasoning': {\n                  for (const detail of part.details) {\n                    switch (detail.type) {\n                      case 'text':\n                        content.push({\n                          type: 'reasoning' as const,\n                          text: detail.text,\n                          signature: detail.signature,\n                        });\n                        break;\n                      case 'redacted':\n                        content.push({\n                          type: 'redacted-reasoning' as const,\n                          data: detail.data,\n                        });\n                        break;\n                    }\n                  }\n                  break;\n                }\n                case 'tool-invocation':\n                  content.push({\n                    type: 'tool-call' as const,\n                    toolCallId: part.toolInvocation.toolCallId,\n                    toolName: part.toolInvocation.toolName,\n                    args: part.toolInvocation.args,\n                  });\n                  break;\n                default: {\n                  const _exhaustiveCheck: never = part;\n                  throw new Error(`Unsupported part: ${_exhaustiveCheck}`);\n                }\n              }\n            }\n\n            coreMessages.push({\n              role: 'assistant',\n              content,\n            });\n\n            // check if there are tool invocations with results in the block\n            const stepInvocations = block\n              .filter(\n                (\n                  part:\n                    | TextUIPart\n                    | ToolInvocationUIPart\n                    | ReasoningUIPart\n                    | FileUIPart,\n                ): part is ToolInvocationUIPart =>\n                  part.type === 'tool-invocation',\n              )\n              .map(part => part.toolInvocation);\n\n            // tool message with tool results\n            if (stepInvocations.length > 0) {\n              coreMessages.push({\n                role: 'tool',\n                content: stepInvocations.map(\n                  (toolInvocation): ToolResultPart => {\n                    if (!('result' in toolInvocation)) {\n                      throw new MessageConversionError({\n                        originalMessage: message,\n                        message:\n                          'ToolInvocation must have a result: ' +\n                          JSON.stringify(toolInvocation),\n                      });\n                    }\n\n                    const { toolCallId, toolName, result } = toolInvocation;\n\n                    const tool = tools[toolName];\n                    return tool?.experimental_toToolResultContent != null\n                      ? {\n                          type: 'tool-result',\n                          toolCallId,\n                          toolName,\n                          result: tool.experimental_toToolResultContent(result),\n                          experimental_content:\n                            tool.experimental_toToolResultContent(result),\n                        }\n                      : {\n                          type: 'tool-result',\n                          toolCallId,\n                          toolName,\n                          result,\n                        };\n                  },\n                ),\n              });\n            }\n\n            // updates for next block\n            block = [];\n            blockHasToolInvocations = false;\n            currentStep++;\n          }\n\n          for (const part of message.parts) {\n            switch (part.type) {\n              case 'text': {\n                if (blockHasToolInvocations) {\n                  processBlock(); // text must come before tool invocations\n                }\n                block.push(part);\n                break;\n              }\n              case 'file':\n              case 'reasoning': {\n                block.push(part);\n                break;\n              }\n              case 'tool-invocation': {\n                if ((part.toolInvocation.step ?? 0) !== currentStep) {\n                  processBlock();\n                }\n                block.push(part);\n                blockHasToolInvocations = true;\n                break;\n              }\n            }\n          }\n\n          processBlock();\n\n          break;\n        }\n\n        const toolInvocations = message.toolInvocations;\n\n        if (toolInvocations == null || toolInvocations.length === 0) {\n          coreMessages.push({ role: 'assistant', content });\n          break;\n        }\n\n        const maxStep = toolInvocations.reduce((max, toolInvocation) => {\n          return Math.max(max, toolInvocation.step ?? 0);\n        }, 0);\n\n        for (let i = 0; i <= maxStep; i++) {\n          const stepInvocations = toolInvocations.filter(\n            toolInvocation => (toolInvocation.step ?? 0) === i,\n          );\n\n          if (stepInvocations.length === 0) {\n            continue;\n          }\n\n          // assistant message with tool calls\n          coreMessages.push({\n            role: 'assistant',\n            content: [\n              ...(isLastMessage && content && i === 0\n                ? [{ type: 'text' as const, text: content }]\n                : []),\n              ...stepInvocations.map(\n                ({ toolCallId, toolName, args }): ToolCallPart => ({\n                  type: 'tool-call' as const,\n                  toolCallId,\n                  toolName,\n                  args,\n                }),\n              ),\n            ],\n          });\n\n          // tool message with tool results\n          coreMessages.push({\n            role: 'tool',\n            content: stepInvocations.map((toolInvocation): ToolResultPart => {\n              if (!('result' in toolInvocation)) {\n                throw new MessageConversionError({\n                  originalMessage: message,\n                  message:\n                    'ToolInvocation must have a result: ' +\n                    JSON.stringify(toolInvocation),\n                });\n              }\n\n              const { toolCallId, toolName, result } = toolInvocation;\n\n              const tool = tools[toolName];\n              return tool?.experimental_toToolResultContent != null\n                ? {\n                    type: 'tool-result',\n                    toolCallId,\n                    toolName,\n                    result: tool.experimental_toToolResultContent(result),\n                    experimental_content:\n                      tool.experimental_toToolResultContent(result),\n                  }\n                : {\n                    type: 'tool-result',\n                    toolCallId,\n                    toolName,\n                    result,\n                  };\n            }),\n          });\n        }\n\n        if (content && !isLastMessage) {\n          coreMessages.push({ role: 'assistant', content });\n        }\n\n        break;\n      }\n\n      case 'data': {\n        // ignore\n        break;\n      }\n\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new MessageConversionError({\n          originalMessage: message,\n          message: `Unsupported role: ${_exhaustiveCheck}`,\n        });\n      }\n    }\n  }\n\n  return coreMessages;\n}\n", "import { z } from 'zod';\nimport { ProviderMetadata } from '../types';\nimport {\n  providerMetadataSchema,\n  ProviderOptions,\n} from '../types/provider-metadata';\nimport {\n  FilePart,\n  filePartSchema,\n  ImagePart,\n  imagePartSchema,\n  ReasoningPart,\n  reasoningPartSchema,\n  RedactedReasoningPart,\n  redactedReasoningPartSchema,\n  TextPart,\n  textPartSchema,\n  ToolCallPart,\n  toolCallPartSchema,\n  ToolResultPart,\n  toolResultPartSchema,\n} from './content-part';\n\n/**\n A system message. It can contain system information.\n\n Note: using the \"system\" part of the prompt is strongly preferred\n to increase the resilience against prompt injection attacks,\n and because not all providers support several system messages.\n */\nexport type CoreSystemMessage = {\n  role: 'system';\n  content: string;\n\n  /**\nAdditional provider-specific metadata. They are passed through\nto the provider from the AI SDK and enable provider-specific\nfunctionality that can be fully encapsulated in the provider.\n */\n  providerOptions?: ProviderOptions;\n\n  /**\n@deprecated Use `providerOptions` instead.\n */\n  experimental_providerMetadata?: ProviderMetadata;\n};\n\nexport const coreSystemMessageSchema: z.ZodType<CoreSystemMessage> = z.object({\n  role: z.literal('system'),\n  content: z.string(),\n  providerOptions: providerMetadataSchema.optional(),\n  experimental_providerMetadata: providerMetadataSchema.optional(),\n});\n\n/**\nA user message. It can contain text or a combination of text and images.\n */\nexport type CoreUserMessage = {\n  role: 'user';\n  content: UserContent;\n\n  /**\nAdditional provider-specific metadata. They are passed through\nto the provider from the AI SDK and enable provider-specific\nfunctionality that can be fully encapsulated in the provider.\n */\n  providerOptions?: ProviderOptions;\n\n  /**\n@deprecated Use `providerOptions` instead.\n*/\n  experimental_providerMetadata?: ProviderMetadata;\n};\n\nexport const coreUserMessageSchema: z.ZodType<CoreUserMessage> = z.object({\n  role: z.literal('user'),\n  content: z.union([\n    z.string(),\n    z.array(z.union([textPartSchema, imagePartSchema, filePartSchema])),\n  ]),\n  providerOptions: providerMetadataSchema.optional(),\n  experimental_providerMetadata: providerMetadataSchema.optional(),\n});\n\n/**\nContent of a user message. It can be a string or an array of text and image parts.\n */\nexport type UserContent = string | Array<TextPart | ImagePart | FilePart>;\n\n/**\nAn assistant message. It can contain text, tool calls, or a combination of text and tool calls.\n */\nexport type CoreAssistantMessage = {\n  role: 'assistant';\n  content: AssistantContent;\n\n  /**\nAdditional provider-specific metadata. They are passed through\nto the provider from the AI SDK and enable provider-specific\nfunctionality that can be fully encapsulated in the provider.\n */\n  providerOptions?: ProviderOptions;\n\n  /**\n@deprecated Use `providerOptions` instead.\n*/\n  experimental_providerMetadata?: ProviderMetadata;\n};\n\nexport const coreAssistantMessageSchema: z.ZodType<CoreAssistantMessage> =\n  z.object({\n    role: z.literal('assistant'),\n    content: z.union([\n      z.string(),\n      z.array(\n        z.union([\n          textPartSchema,\n          filePartSchema,\n          reasoningPartSchema,\n          redactedReasoningPartSchema,\n          toolCallPartSchema,\n        ]),\n      ),\n    ]),\n    providerOptions: providerMetadataSchema.optional(),\n    experimental_providerMetadata: providerMetadataSchema.optional(),\n  });\n\n/**\nContent of an assistant message.\nIt can be a string or an array of text, image, reasoning, redacted reasoning, and tool call parts.\n */\nexport type AssistantContent =\n  | string\n  | Array<\n      TextPart | FilePart | ReasoningPart | RedactedReasoningPart | ToolCallPart\n    >;\n\n/**\nA tool message. It contains the result of one or more tool calls.\n */\nexport type CoreToolMessage = {\n  role: 'tool';\n  content: ToolContent;\n\n  /**\nAdditional provider-specific metadata. They are passed through\nto the provider from the AI SDK and enable provider-specific\nfunctionality that can be fully encapsulated in the provider.\n */\n  providerOptions?: ProviderOptions;\n\n  /**\n@deprecated Use `providerOptions` instead.\n*/\n  experimental_providerMetadata?: ProviderMetadata;\n};\n\nexport const coreToolMessageSchema: z.ZodType<CoreToolMessage> = z.object({\n  role: z.literal('tool'),\n  content: z.array(toolResultPartSchema),\n  providerOptions: providerMetadataSchema.optional(),\n  experimental_providerMetadata: providerMetadataSchema.optional(),\n});\n\n/**\nContent of a tool message. It is an array of tool result parts.\n */\nexport type ToolContent = Array<ToolResultPart>;\n\n/**\nA message that can be used in the `messages` field of a prompt.\nIt can be a user message, an assistant message, or a tool message.\n */\nexport type CoreMessage =\n  | CoreSystemMessage\n  | CoreUserMessage\n  | CoreAssistantMessage\n  | CoreToolMessage;\n\nexport const coreMessageSchema: z.ZodType<CoreMessage> = z.union([\n  coreSystemMessageSchema,\n  coreUserMessageSchema,\n  coreAssistantMessageSchema,\n  coreToolMessageSchema,\n]);\n", "import { LanguageModelV1ProviderMetadata } from '@ai-sdk/provider';\nimport { z } from 'zod';\nimport { jsonValueSchema } from './json-value';\n\n/**\nAdditional provider-specific metadata that is returned from the provider.\n\nThis is needed to enable provider-specific functionality that can be\nfully encapsulated in the provider.\n */\nexport type ProviderMetadata = LanguageModelV1ProviderMetadata;\n\n/**\nAdditional provider-specific options.\n\nThey are passed through to the provider from the AI SDK and enable\nprovider-specific functionality that can be fully encapsulated in the provider.\n */\n// TODO change to LanguageModelV2ProviderOptions in language model v2\nexport type ProviderOptions = LanguageModelV1ProviderMetadata;\n\nexport const providerMetadataSchema: z.ZodType<ProviderMetadata> = z.record(\n  z.string(),\n  z.record(z.string(), jsonValueSchema),\n);\n", "import { JSONValue } from '@ai-sdk/provider';\nimport { z } from 'zod';\n\nexport const jsonValueSchema: z.ZodType<JSONValue> = z.lazy(() =>\n  z.union([\n    z.null(),\n    z.string(),\n    z.number(),\n    z.boolean(),\n    z.record(z.string(), jsonValueSchema),\n    z.array(jsonValueSchema),\n  ]),\n);\n", "import { z } from 'zod';\nimport {\n  ProviderMetadata,\n  providerMetadataSchema,\n  ProviderOptions,\n} from '../types/provider-metadata';\nimport { DataContent, dataContentSchema } from './data-content';\nimport {\n  ToolResultContent,\n  toolResultContentSchema,\n} from './tool-result-content';\n\n/**\nText content part of a prompt. It contains a string of text.\n */\nexport interface TextPart {\n  type: 'text';\n\n  /**\nThe text content.\n   */\n  text: string;\n\n  /**\nAdditional provider-specific metadata. They are passed through\nto the provider from the AI SDK and enable provider-specific\nfunctionality that can be fully encapsulated in the provider.\n */\n  providerOptions?: ProviderOptions;\n\n  /**\n@deprecated Use `providerOptions` instead.\n */\n  experimental_providerMetadata?: ProviderMetadata;\n}\n\n/**\n@internal\n */\nexport const textPartSchema: z.ZodType<TextPart> = z.object({\n  type: z.literal('text'),\n  text: z.string(),\n  providerOptions: providerMetadataSchema.optional(),\n  experimental_providerMetadata: providerMetadataSchema.optional(),\n});\n\n/**\nImage content part of a prompt. It contains an image.\n */\nexport interface ImagePart {\n  type: 'image';\n\n  /**\nImage data. Can either be:\n\n- data: a base64-encoded string, a Uint8Array, an ArrayBuffer, or a Buffer\n- URL: a URL that points to the image\n   */\n  image: DataContent | URL;\n\n  /**\nOptional mime type of the image.\n   */\n  mimeType?: string;\n\n  /**\nAdditional provider-specific metadata. They are passed through\nto the provider from the AI SDK and enable provider-specific\nfunctionality that can be fully encapsulated in the provider.\n */\n  providerOptions?: ProviderOptions;\n\n  /**\n@deprecated Use `providerOptions` instead.\n */\n  experimental_providerMetadata?: ProviderMetadata;\n}\n\n/**\n@internal\n */\nexport const imagePartSchema: z.ZodType<ImagePart> = z.object({\n  type: z.literal('image'),\n  image: z.union([dataContentSchema, z.instanceof(URL)]),\n  mimeType: z.string().optional(),\n  providerOptions: providerMetadataSchema.optional(),\n  experimental_providerMetadata: providerMetadataSchema.optional(),\n});\n\n/**\nFile content part of a prompt. It contains a file.\n */\nexport interface FilePart {\n  type: 'file';\n\n  /**\nFile data. Can either be:\n\n- data: a base64-encoded string, a Uint8Array, an ArrayBuffer, or a Buffer\n- URL: a URL that points to the image\n   */\n  data: DataContent | URL;\n\n  /**\nOptional filename of the file.\n   */\n  filename?: string;\n\n  /**\nMime type of the file.\n   */\n  mimeType: string;\n\n  /**\nAdditional provider-specific metadata. They are passed through\nto the provider from the AI SDK and enable provider-specific\nfunctionality that can be fully encapsulated in the provider.\n */\n  providerOptions?: ProviderOptions;\n\n  /**\n@deprecated Use `providerOptions` instead.\n */\n  experimental_providerMetadata?: ProviderMetadata;\n}\n\n/**\n@internal\n */\nexport const filePartSchema: z.ZodType<FilePart> = z.object({\n  type: z.literal('file'),\n  data: z.union([dataContentSchema, z.instanceof(URL)]),\n  filename: z.string().optional(),\n  mimeType: z.string(),\n  providerOptions: providerMetadataSchema.optional(),\n  experimental_providerMetadata: providerMetadataSchema.optional(),\n});\n\n/**\n * Reasoning content part of a prompt. It contains a reasoning.\n */\nexport interface ReasoningPart {\n  type: 'reasoning';\n\n  /**\nThe reasoning text.\n   */\n  text: string;\n\n  /**\nAn optional signature for verifying that the reasoning originated from the model.\n   */\n  signature?: string;\n\n  /**\nAdditional provider-specific metadata. They are passed through\nto the provider from the AI SDK and enable provider-specific\nfunctionality that can be fully encapsulated in the provider.\n */\n  providerOptions?: ProviderOptions;\n\n  /**\n@deprecated Use `providerOptions` instead.\n */\n  experimental_providerMetadata?: ProviderMetadata;\n}\n\n/**\n@internal\n */\nexport const reasoningPartSchema: z.ZodType<ReasoningPart> = z.object({\n  type: z.literal('reasoning'),\n  text: z.string(),\n  providerOptions: providerMetadataSchema.optional(),\n  experimental_providerMetadata: providerMetadataSchema.optional(),\n});\n\n/**\nRedacted reasoning content part of a prompt.\n */\nexport interface RedactedReasoningPart {\n  type: 'redacted-reasoning';\n\n  /**\nRedacted reasoning data.\n   */\n  data: string;\n\n  /**\nAdditional provider-specific metadata. They are passed through\nto the provider from the AI SDK and enable provider-specific\nfunctionality that can be fully encapsulated in the provider.\n */\n  providerOptions?: ProviderOptions;\n\n  /**\n@deprecated Use `providerOptions` instead.\n */\n  experimental_providerMetadata?: ProviderMetadata;\n}\n\n/**\n@internal\n */\nexport const redactedReasoningPartSchema: z.ZodType<RedactedReasoningPart> =\n  z.object({\n    type: z.literal('redacted-reasoning'),\n    data: z.string(),\n    providerOptions: providerMetadataSchema.optional(),\n    experimental_providerMetadata: providerMetadataSchema.optional(),\n  });\n\n/**\nTool call content part of a prompt. It contains a tool call (usually generated by the AI model).\n */\nexport interface ToolCallPart {\n  type: 'tool-call';\n\n  /**\nID of the tool call. This ID is used to match the tool call with the tool result.\n */\n  toolCallId: string;\n\n  /**\nName of the tool that is being called.\n */\n  toolName: string;\n\n  /**\nArguments of the tool call. This is a JSON-serializable object that matches the tool's input schema.\n   */\n  args: unknown;\n\n  /**\nAdditional provider-specific metadata. They are passed through\nto the provider from the AI SDK and enable provider-specific\nfunctionality that can be fully encapsulated in the provider.\n */\n  providerOptions?: ProviderOptions;\n\n  /**\n@deprecated Use `providerOptions` instead.\n */\n  experimental_providerMetadata?: ProviderMetadata;\n}\n\n/**\n@internal\n */\nexport const toolCallPartSchema: z.ZodType<ToolCallPart> = z.object({\n  type: z.literal('tool-call'),\n  toolCallId: z.string(),\n  toolName: z.string(),\n  args: z.unknown(),\n  providerOptions: providerMetadataSchema.optional(),\n  experimental_providerMetadata: providerMetadataSchema.optional(),\n}) as z.ZodType<ToolCallPart>; // necessary bc args is optional on Zod type\n\n/**\nTool result content part of a prompt. It contains the result of the tool call with the matching ID.\n */\nexport interface ToolResultPart {\n  type: 'tool-result';\n\n  /**\nID of the tool call that this result is associated with.\n */\n  toolCallId: string;\n\n  /**\nName of the tool that generated this result.\n  */\n  toolName: string;\n\n  /**\nResult of the tool call. This is a JSON-serializable object.\n   */\n  result: unknown;\n\n  /**\nMulti-part content of the tool result. Only for tools that support multipart results.\n   */\n  experimental_content?: ToolResultContent;\n\n  /**\nOptional flag if the result is an error or an error message.\n   */\n  isError?: boolean;\n\n  /**\nAdditional provider-specific metadata. They are passed through\nto the provider from the AI SDK and enable provider-specific\nfunctionality that can be fully encapsulated in the provider.\n */\n  providerOptions?: ProviderOptions;\n\n  /**\n@deprecated Use `providerOptions` instead.\n */\n  experimental_providerMetadata?: ProviderMetadata;\n}\n\n/**\n@internal\n */\nexport const toolResultPartSchema: z.ZodType<ToolResultPart> = z.object({\n  type: z.literal('tool-result'),\n  toolCallId: z.string(),\n  toolName: z.string(),\n  result: z.unknown(),\n  content: toolResultContentSchema.optional(),\n  isError: z.boolean().optional(),\n  providerOptions: providerMetadataSchema.optional(),\n  experimental_providerMetadata: providerMetadataSchema.optional(),\n}) as z.ZodType<ToolResultPart>; // necessary bc result is optional on Zod type\n", "import { z } from 'zod';\n\nexport type ToolResultContent = Array<\n  | {\n      type: 'text';\n      text: string;\n    }\n  | {\n      type: 'image';\n      data: string; // base64 encoded png image, e.g. screenshot\n      mimeType?: string; // e.g. 'image/png';\n    }\n>;\n\nexport const toolResultContentSchema: z.ZodType<ToolResultContent> = z.array(\n  z.union([\n    z.object({ type: z.literal('text'), text: z.string() }),\n    z.object({\n      type: z.literal('image'),\n      data: z.string(),\n      mimeType: z.string().optional(),\n    }),\n  ]),\n);\n\nexport function isToolResultContent(\n  value: unknown,\n): value is ToolResultContent {\n  if (!Array.isArray(value) || value.length === 0) {\n    return false;\n  }\n\n  return value.every(part => {\n    if (typeof part !== 'object' || part === null) {\n      return false;\n    }\n\n    if (part.type === 'text') {\n      return typeof part.text === 'string';\n    }\n\n    if (part.type === 'image') {\n      return (\n        typeof part.data === 'string' &&\n        (part.mimeType === undefined || typeof part.mimeType === 'string')\n      );\n    }\n\n    return false;\n  });\n}\n", "/**\nRepresents the number of tokens used in a prompt and completion.\n */\nexport type LanguageModelUsage = {\n  /**\nThe number of tokens used in the prompt.\n   */\n  promptTokens: number;\n\n  /**\nThe number of tokens used in the completion.\n */\n  completionTokens: number;\n\n  /**\nThe total number of tokens used (promptTokens + completionTokens).\n   */\n  totalTokens: number;\n};\n\n/**\nRepresents the number of tokens used in an embedding.\n */\nexport type EmbeddingModelUsage = {\n  /**\nThe number of tokens used in the embedding.\n   */\n  tokens: number;\n};\n\nexport function calculateLanguageModelUsage({\n  promptTokens,\n  completionTokens,\n}: {\n  promptTokens: number;\n  completionTokens: number;\n}): LanguageModelUsage {\n  return {\n    promptTokens,\n    completionTokens,\n    totalTokens: promptTokens + completionTokens,\n  };\n}\n\nexport function addLanguageModelUsage(\n  usage1: LanguageModelUsage,\n  usage2: LanguageModelUsage,\n): LanguageModelUsage {\n  return {\n    promptTokens: usage1.promptTokens + usage2.promptTokens,\n    completionTokens: usage1.completionTokens + usage2.completionTokens,\n    totalTokens: usage1.totalTokens + usage2.totalTokens,\n  };\n}\n", "import { AISDKError, getErrorMessage } from '@ai-sdk/provider';\n\nconst name = 'AI_InvalidToolArgumentsError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class InvalidToolArgumentsError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly toolName: string;\n  readonly toolArgs: string;\n\n  constructor({\n    toolArgs,\n    toolName,\n    cause,\n    message = `Invalid arguments for tool ${toolName}: ${getErrorMessage(\n      cause,\n    )}`,\n  }: {\n    message?: string;\n    toolArgs: string;\n    toolName: string;\n    cause: unknown;\n  }) {\n    super({ name, message, cause });\n\n    this.toolArgs = toolArgs;\n    this.toolName = toolName;\n  }\n\n  static isInstance(error: unknown): error is InvalidToolArgumentsError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from '@ai-sdk/provider';\n\nconst name = 'AI_NoSuchToolError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class NoSuchToolError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly toolName: string;\n  readonly availableTools: string[] | undefined;\n\n  constructor({\n    toolName,\n    availableTools = undefined,\n    message = `Model tried to call unavailable tool '${toolName}'. ${\n      availableTools === undefined\n        ? 'No tools are available.'\n        : `Available tools: ${availableTools.join(', ')}.`\n    }`,\n  }: {\n    toolName: string;\n    availableTools?: string[] | undefined;\n    message?: string;\n  }) {\n    super({ name, message });\n\n    this.toolName = toolName;\n    this.availableTools = availableTools;\n  }\n\n  static isInstance(error: unknown): error is NoSuchToolError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "export function isAsyncGenerator<T, TReturn, TNext>(\n  value: unknown,\n): value is AsyncGenerator<T, TReturn, TNext> {\n  return (\n    value != null && typeof value === 'object' && Symbol.asyncIterator in value\n  );\n}\n", "export function isGenerator<T, TReturn, TNext>(\n  value: unknown,\n): value is Generator<T, TReturn, TNext> {\n  return value != null && typeof value === 'object' && Symbol.iterator in value;\n}\n", "/**\n * Warning time for notifying developers that a stream is hanging in dev mode\n * using a console.warn.\n */\nexport const HANGING_STREAM_WARNING_TIME_MS = 15 * 1000;\n", "import React, { Suspense } from 'react';\nimport { createResolvablePromise } from '../../util/create-resolvable-promise';\n\n// Recursive type for the chunk.\ntype ChunkType =\n  | {\n      done: false;\n      value: React.ReactNode;\n      next: Promise<ChunkType>;\n      append?: boolean;\n    }\n  | {\n      done: true;\n      value: React.ReactNode;\n    };\n\n// Use single letter names for the variables to reduce the size of the RSC payload.\n// `R` for `Row`, `c` for `current`, `n` for `next`.\n// Note: Array construction is needed to access the name R.\nconst R = [\n  (async ({\n    c: current,\n    n: next,\n  }: {\n    c: React.ReactNode;\n    n: Promise<ChunkType>;\n  }) => {\n    const chunk = await next;\n\n    if (chunk.done) {\n      return chunk.value;\n    }\n\n    if (chunk.append) {\n      return (\n        <>\n          {current}\n          <Suspense fallback={chunk.value}>\n            <R c={chunk.value} n={chunk.next} />\n          </Suspense>\n        </>\n      );\n    }\n\n    return (\n      <Suspense fallback={chunk.value}>\n        <R c={chunk.value} n={chunk.next} />\n      </Suspense>\n    );\n  }) as unknown as React.FC<{\n    c: React.ReactNode;\n    n: Promise<ChunkType>;\n  }>,\n][0];\n\n/**\n * Creates a suspended chunk for React Server Components.\n *\n * This function generates a suspenseful React component that can be dynamically updated.\n * It's useful for streaming updates to the client in a React Server Components context.\n *\n * @param {React.ReactNode} initialValue - The initial value to render while the promise is pending.\n * @returns {Object} An object containing:\n *   - row: A React node that renders the suspenseful content.\n *   - resolve: A function to resolve the promise with a new value.\n *   - reject: A function to reject the promise with an error.\n */\nexport function createSuspendedChunk(initialValue: React.ReactNode): {\n  row: React.ReactNode;\n  resolve: (value: ChunkType) => void;\n  reject: (error: unknown) => void;\n} {\n  const { promise, resolve, reject } = createResolvablePromise<ChunkType>();\n\n  return {\n    row: (\n      <Suspense fallback={initialValue}>\n        <R c={initialValue} n={promise} />\n      </Suspense>\n    ),\n    resolve,\n    reject,\n  };\n}\n", "import { HANGING_STREAM_WARNING_TIME_MS } from '../../util/constants';\nimport { createResolvablePromise } from '../../util/create-resolvable-promise';\nimport { createSuspendedChunk } from './create-suspended-chunk';\n\n// It's necessary to define the type manually here, otherwise TypeScript compiler\n// will not be able to infer the correct return type as it's circular.\ntype StreamableUIWrapper = {\n  /**\n   * The value of the streamable UI. This can be returned from a Server Action and received by the client.\n   */\n  readonly value: React.ReactNode;\n\n  /**\n   * This method updates the current UI node. It takes a new UI node and replaces the old one.\n   */\n  update(value: React.ReactNode): StreamableUIWrapper;\n\n  /**\n   * This method is used to append a new UI node to the end of the old one.\n   * Once appended a new UI node, the previous UI node cannot be updated anymore.\n   *\n   * @example\n   * ```jsx\n   * const ui = createStreamableUI(<div>hello</div>)\n   * ui.append(<div>world</div>)\n   *\n   * // The UI node will be:\n   * // <>\n   * //   <div>hello</div>\n   * //   <div>world</div>\n   * // </>\n   * ```\n   */\n  append(value: React.ReactNode): StreamableUIWrapper;\n\n  /**\n   * This method is used to signal that there is an error in the UI stream.\n   * It will be thrown on the client side and caught by the nearest error boundary component.\n   */\n  error(error: any): StreamableUIWrapper;\n\n  /**\n   * This method marks the UI node as finalized. You can either call it without any parameters or with a new UI node as the final state.\n   * Once called, the UI node cannot be updated or appended anymore.\n   *\n   * This method is always **required** to be called, otherwise the response will be stuck in a loading state.\n   */\n  done(...args: [React.ReactNode] | []): StreamableUIWrapper;\n};\n\n/**\n * Create a piece of changeable UI that can be streamed to the client.\n * On the client side, it can be rendered as a normal React node.\n */\nfunction createStreamableUI(initialValue?: React.ReactNode) {\n  let currentValue = initialValue;\n  let closed = false;\n  let { row, resolve, reject } = createSuspendedChunk(initialValue);\n\n  function assertStream(method: string) {\n    if (closed) {\n      throw new Error(method + ': UI stream is already closed.');\n    }\n  }\n\n  let warningTimeout: NodeJS.Timeout | undefined;\n  function warnUnclosedStream() {\n    if (process.env.NODE_ENV === 'development') {\n      if (warningTimeout) {\n        clearTimeout(warningTimeout);\n      }\n      warningTimeout = setTimeout(() => {\n        console.warn(\n          'The streamable UI has been slow to update. This may be a bug or a performance issue or you forgot to call `.done()`.',\n        );\n      }, HANGING_STREAM_WARNING_TIME_MS);\n    }\n  }\n  warnUnclosedStream();\n\n  const streamable: StreamableUIWrapper = {\n    value: row,\n    update(value: React.ReactNode) {\n      assertStream('.update()');\n\n      // There is no need to update the value if it's referentially equal.\n      if (value === currentValue) {\n        warnUnclosedStream();\n        return streamable;\n      }\n\n      const resolvable = createResolvablePromise();\n      currentValue = value;\n\n      resolve({ value: currentValue, done: false, next: resolvable.promise });\n      resolve = resolvable.resolve;\n      reject = resolvable.reject;\n\n      warnUnclosedStream();\n\n      return streamable;\n    },\n    append(value: React.ReactNode) {\n      assertStream('.append()');\n\n      const resolvable = createResolvablePromise();\n      currentValue = value;\n\n      resolve({ value, done: false, append: true, next: resolvable.promise });\n      resolve = resolvable.resolve;\n      reject = resolvable.reject;\n\n      warnUnclosedStream();\n\n      return streamable;\n    },\n    error(error: any) {\n      assertStream('.error()');\n\n      if (warningTimeout) {\n        clearTimeout(warningTimeout);\n      }\n      closed = true;\n      reject(error);\n\n      return streamable;\n    },\n    done(...args: [] | [React.ReactNode]) {\n      assertStream('.done()');\n\n      if (warningTimeout) {\n        clearTimeout(warningTimeout);\n      }\n      closed = true;\n      if (args.length) {\n        resolve({ value: args[0], done: true });\n        return streamable;\n      }\n      resolve({ value: currentValue, done: true });\n\n      return streamable;\n    },\n  };\n\n  return streamable;\n}\n\nexport { createStreamableUI };\n", "export const STREAMABLE_VALUE_TYPE = Symbol.for('ui.streamable.value');\n\nexport type StreamablePatch = undefined | [0, string]; // Append string.\n\ndeclare const __internal_curr: unique symbol;\ndeclare const __internal_error: unique symbol;\n\n/**\n * StreamableValue is a value that can be streamed over the network via AI Actions.\n * To read the streamed values, use the `readStreamableValue` or `useStreamableValue` APIs.\n */\nexport type StreamableValue<T = any, E = any> = {\n  /**\n   * @internal Use `readStreamableValue` to read the values.\n   */\n  type?: typeof STREAMABLE_VALUE_TYPE;\n  /**\n   * @internal Use `readStreamableValue` to read the values.\n   */\n  curr?: T;\n  /**\n   * @internal Use `readStreamableValue` to read the values.\n   */\n  error?: E;\n  /**\n   * @internal Use `readStreamableValue` to read the values.\n   */\n  diff?: StreamablePatch;\n  /**\n   * @internal Use `readStreamableValue` to read the values.\n   */\n  next?: Promise<StreamableValue<T, E>>;\n\n  // branded types to maintain type signature after internal properties are stripped.\n  [__internal_curr]?: T;\n  [__internal_error]?: E;\n};\n", "import { HANGING_STREAM_WARNING_TIME_MS } from '../../util/constants';\nimport { createResolvablePromise } from '../../util/create-resolvable-promise';\nimport {\n  STREAMABLE_VALUE_TYPE,\n  StreamablePatch,\n  StreamableValue,\n} from './streamable-value';\n\nconst STREAMABLE_VALUE_INTERNAL_LOCK = Symbol('streamable.value.lock');\n\n/**\n * Create a wrapped, changeable value that can be streamed to the client.\n * On the client side, the value can be accessed via the readStreamableValue() API.\n */\nfunction createStreamableValue<T = any, E = any>(\n  initialValue?: T | ReadableStream<T>,\n) {\n  const isReadableStream =\n    initialValue instanceof ReadableStream ||\n    (typeof initialValue === 'object' &&\n      initialValue !== null &&\n      'getReader' in initialValue &&\n      typeof initialValue.getReader === 'function' &&\n      'locked' in initialValue &&\n      typeof initialValue.locked === 'boolean');\n\n  if (!isReadableStream) {\n    return createStreamableValueImpl<T, E>(initialValue);\n  }\n\n  const streamableValue = createStreamableValueImpl<T, E>();\n\n  // Since the streamable value will be from a readable stream, it's not allowed\n  // to update the value manually as that introduces race conditions and\n  // unexpected behavior.\n  // We lock the value to prevent any updates from the user.\n  streamableValue[STREAMABLE_VALUE_INTERNAL_LOCK] = true;\n\n  (async () => {\n    try {\n      // Consume the readable stream and update the value.\n      const reader = initialValue.getReader();\n\n      while (true) {\n        const { value, done } = await reader.read();\n        if (done) {\n          break;\n        }\n\n        // Unlock the value to allow updates.\n        streamableValue[STREAMABLE_VALUE_INTERNAL_LOCK] = false;\n        if (typeof value === 'string') {\n          streamableValue.append(value);\n        } else {\n          streamableValue.update(value);\n        }\n        // Lock the value again.\n        streamableValue[STREAMABLE_VALUE_INTERNAL_LOCK] = true;\n      }\n\n      streamableValue[STREAMABLE_VALUE_INTERNAL_LOCK] = false;\n      streamableValue.done();\n    } catch (e) {\n      streamableValue[STREAMABLE_VALUE_INTERNAL_LOCK] = false;\n      streamableValue.error(e);\n    }\n  })();\n\n  return streamableValue;\n}\n\n// It's necessary to define the type manually here, otherwise TypeScript compiler\n// will not be able to infer the correct return type as it's circular.\ntype StreamableValueWrapper<T, E> = {\n  /**\n   * The value of the streamable. This can be returned from a Server Action and\n   * received by the client. To read the streamed values, use the\n   * `readStreamableValue` or `useStreamableValue` APIs.\n   */\n  readonly value: StreamableValue<T, E>;\n\n  /**\n   * This method updates the current value with a new one.\n   */\n  update(value: T): StreamableValueWrapper<T, E>;\n\n  /**\n   * This method is used to append a delta string to the current value. It\n   * requires the current value of the streamable to be a string.\n   *\n   * @example\n   * ```jsx\n   * const streamable = createStreamableValue('hello');\n   * streamable.append(' world');\n   *\n   * // The value will be 'hello world'\n   * ```\n   */\n  append(value: T): StreamableValueWrapper<T, E>;\n\n  /**\n   * This method is used to signal that there is an error in the value stream.\n   * It will be thrown on the client side when consumed via\n   * `readStreamableValue` or `useStreamableValue`.\n   */\n  error(error: any): StreamableValueWrapper<T, E>;\n\n  /**\n   * This method marks the value as finalized. You can either call it without\n   * any parameters or with a new value as the final state.\n   * Once called, the value cannot be updated or appended anymore.\n   *\n   * This method is always **required** to be called, otherwise the response\n   * will be stuck in a loading state.\n   */\n  done(...args: [T] | []): StreamableValueWrapper<T, E>;\n\n  /**\n   * @internal This is an internal lock to prevent the value from being\n   * updated by the user.\n   */\n  [STREAMABLE_VALUE_INTERNAL_LOCK]: boolean;\n};\n\nfunction createStreamableValueImpl<T = any, E = any>(initialValue?: T) {\n  let closed = false;\n  let locked = false;\n  let resolvable = createResolvablePromise<StreamableValue<T, E>>();\n\n  let currentValue = initialValue;\n  let currentError: E | undefined;\n  let currentPromise: typeof resolvable.promise | undefined =\n    resolvable.promise;\n  let currentPatchValue: StreamablePatch;\n\n  function assertStream(method: string) {\n    if (closed) {\n      throw new Error(method + ': Value stream is already closed.');\n    }\n    if (locked) {\n      throw new Error(\n        method + ': Value stream is locked and cannot be updated.',\n      );\n    }\n  }\n\n  let warningTimeout: NodeJS.Timeout | undefined;\n  function warnUnclosedStream() {\n    if (process.env.NODE_ENV === 'development') {\n      if (warningTimeout) {\n        clearTimeout(warningTimeout);\n      }\n      warningTimeout = setTimeout(() => {\n        console.warn(\n          'The streamable value has been slow to update. This may be a bug or a performance issue or you forgot to call `.done()`.',\n        );\n      }, HANGING_STREAM_WARNING_TIME_MS);\n    }\n  }\n  warnUnclosedStream();\n\n  function createWrapped(initialChunk?: boolean): StreamableValue<T, E> {\n    // This makes the payload much smaller if there're mutative updates before the first read.\n    let init: Partial<StreamableValue<T, E>>;\n\n    if (currentError !== undefined) {\n      init = { error: currentError };\n    } else {\n      if (currentPatchValue && !initialChunk) {\n        init = { diff: currentPatchValue };\n      } else {\n        init = { curr: currentValue };\n      }\n    }\n\n    if (currentPromise) {\n      init.next = currentPromise;\n    }\n\n    if (initialChunk) {\n      init.type = STREAMABLE_VALUE_TYPE;\n    }\n\n    return init;\n  }\n\n  // Update the internal `currentValue` and `currentPatchValue` if needed.\n  function updateValueStates(value: T) {\n    // If we can only send a patch over the wire, it's better to do so.\n    currentPatchValue = undefined;\n    if (typeof value === 'string') {\n      if (typeof currentValue === 'string') {\n        if (value.startsWith(currentValue)) {\n          currentPatchValue = [0, value.slice(currentValue.length)];\n        }\n      }\n    }\n\n    currentValue = value;\n  }\n\n  const streamable: StreamableValueWrapper<T, E> = {\n    set [STREAMABLE_VALUE_INTERNAL_LOCK](state: boolean) {\n      locked = state;\n    },\n    get value() {\n      return createWrapped(true);\n    },\n    update(value: T) {\n      assertStream('.update()');\n\n      const resolvePrevious = resolvable.resolve;\n      resolvable = createResolvablePromise();\n\n      updateValueStates(value);\n      currentPromise = resolvable.promise;\n      resolvePrevious(createWrapped());\n\n      warnUnclosedStream();\n\n      return streamable;\n    },\n    append(value: T) {\n      assertStream('.append()');\n\n      if (\n        typeof currentValue !== 'string' &&\n        typeof currentValue !== 'undefined'\n      ) {\n        throw new Error(\n          `.append(): The current value is not a string. Received: ${typeof currentValue}`,\n        );\n      }\n      if (typeof value !== 'string') {\n        throw new Error(\n          `.append(): The value is not a string. Received: ${typeof value}`,\n        );\n      }\n\n      const resolvePrevious = resolvable.resolve;\n      resolvable = createResolvablePromise();\n\n      if (typeof currentValue === 'string') {\n        currentPatchValue = [0, value];\n        (currentValue as string) = currentValue + value;\n      } else {\n        currentPatchValue = undefined;\n        currentValue = value;\n      }\n\n      currentPromise = resolvable.promise;\n      resolvePrevious(createWrapped());\n\n      warnUnclosedStream();\n\n      return streamable;\n    },\n    error(error: any) {\n      assertStream('.error()');\n\n      if (warningTimeout) {\n        clearTimeout(warningTimeout);\n      }\n      closed = true;\n      currentError = error;\n      currentPromise = undefined;\n\n      resolvable.resolve({ error });\n\n      return streamable;\n    },\n    done(...args: [] | [T]) {\n      assertStream('.done()');\n\n      if (warningTimeout) {\n        clearTimeout(warningTimeout);\n      }\n      closed = true;\n      currentPromise = undefined;\n\n      if (args.length) {\n        updateValueStates(args[0]);\n        resolvable.resolve(createWrapped());\n        return streamable;\n      }\n\n      resolvable.resolve({});\n\n      return streamable;\n    },\n  };\n\n  return streamable;\n}\n\nexport { createStreamableValue };\n"], "mappings": ";AAAA,YAAY,mBAAmB;AAC/B,SAAS,yBAAyB;;;ACQ3B,SAAS,0BAId;AACA,MAAI;AACJ,MAAI;AAEJ,QAAM,UAAU,IAAI,QAAW,CAAC,KAAK,QAAQ;AAC3C,cAAU;AACV,aAAS;AAAA,EACX,CAAC;AAED,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ACrBO,IAAM,aAAa,CAAC,UACzB,OAAO,UAAU;;;AFOnB,IAAM,sBAAsB,IAAI,kBAO7B;AAEH,SAAS,uBAAuB,SAAiB;AAC/C,QAAM,QAAQ,oBAAoB,SAAS;AAC3C,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,OAAO;AAAA,EACzB;AACA,SAAO;AACT;AAEO,SAAS,YACd,EAAE,OAAO,QAAQ,GACjB,IACG;AACH,SAAO,oBAAoB;AAAA,IACzB;AAAA,MACE,cAAc,KAAK,MAAM,KAAK,UAAU,KAAK,CAAC;AAAA;AAAA,MAC9C,eAAe;AAAA,MACf,QAAQ;AAAA,MACR;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,yBAAyB;AACvC,QAAM,QAAQ,uBAAuB,0BAA0B;AAC/D,SAAO,MAAM;AACf;AAKO,SAAS,qBAAqB;AACnC,QAAM,QAAQ,uBAAuB,0BAA0B;AAC/D,QAAM,SAAS;AACjB;AAgBA,SAAS,cACJ,MACH;AACA,QAAM,QAAQ;AAAA,IACZ;AAAA,EACF;AAEA,MAAI,KAAK,SAAS,GAAG;AACnB,UAAM,MAAM,KAAK,CAAC;AAClB,QAAI,OAAO,MAAM,iBAAiB,UAAU;AAC1C,YAAM,IAAI;AAAA,QACR,sBAAsB;AAAA,UACpB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO,MAAM,aAAa,GAAsC;AAAA,EAClE;AAEA,SAAO,MAAM;AACf;AA0BA,SAAS,qBACJ,MACH;AAOA,QAAM,QAAQ;AAAA,IACZ;AAAA,EACF;AAEA,MAAI,MAAM,QAAQ;AAChB,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,MAAM,sBAAsB;AAC/B,UAAM,EAAE,SAAS,QAAQ,IAAI,wBAAwB;AACrD,UAAM,uBAAuB;AAC7B,UAAM,uBAAuB;AAAA,EAC/B;AAEA,WAAS,SAAS,UAA6B,MAAe;AAhJhE,QAAAA,KAAA;AAiJI,QAAI,KAAK,SAAS,GAAG;AACnB,UAAI,OAAO,MAAM,iBAAiB,UAAU;AAC1C,cAAM,MAAM,KAAK,CAAC;AAClB,cAAM,IAAI;AAAA,UACR,yBAAyB;AAAA,YACvB;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAEA,QAAI,WAAW,QAAQ,GAAG;AACxB,UAAI,KAAK,SAAS,GAAG;AACnB,cAAM,aAAa,KAAK,CAAC,CAAC,IAAI,SAAS,MAAM,aAAa,KAAK,CAAC,CAAC,CAAC;AAAA,MACpE,OAAO;AACL,cAAM,eAAe,SAAS,MAAM,YAAY;AAAA,MAClD;AAAA,IACF,OAAO;AACL,UAAI,KAAK,SAAS,GAAG;AACnB,cAAM,aAAa,KAAK,CAAC,CAAC,IAAI;AAAA,MAChC,OAAO;AACL,cAAM,eAAe;AAAA,MACvB;AAAA,IACF;AAEA,WAAAA,MAAA,MAAM,SAAQ,iBAAd,wBAAAA,KAA6B;AAAA,MAC3B,KAAK,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI;AAAA,MACjC,OAAO,MAAM;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAEA,QAAM,eAAe;AAAA,IACnB,KAAK,MAAM;AACT,UAAI,KAAK,SAAS,GAAG;AACnB,cAAM,MAAM,KAAK,CAAC;AAClB,YAAI,OAAO,MAAM,iBAAiB,UAAU;AAC1C,gBAAM,IAAI;AAAA,YACR,sBAAsB;AAAA,cACpB;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AACA,eAAO,MAAM,aAAa,GAAG;AAAA,MAC/B;AAEA,aAAO,MAAM;AAAA,IACf;AAAA,IACA,QAAQ,SAAS,OAAO,YAA+B;AACrD,eAAS,YAAY,KAAK;AAAA,IAC5B;AAAA,IACA,MAAM,SAAS,QAAQ,UAAoC;AACzD,UAAI,SAAS,SAAS,GAAG;AACvB,iBAAS,SAAS,CAAC,GAAwB,IAAI;AAAA,MACjD;AAEA,YAAM,QAAsB,mBAAK,MAAM,eAAe,MAAM,YAAY;AACxE,YAAM,qBAAsB,KAAK;AAAA,IACnC;AAAA,EACF;AAEA,SAAO;AACT;;;AG7MA,YAAY,WAAW;AACvB,SAAS,0BAA0B;AAoI7B;AApHN,eAAe,YACb;AAAA,EACE;AAAA,EACA;AACF,GACA,UACG,MACH;AACA;AACA,SAAO,MAAM;AAAA,IACX;AAAA,MACE;AAAA,MACA;AAAA,IACF;AAAA,IACA,YAAY;AACV,YAAM,SAAS,MAAM,OAAO,GAAG,IAAI;AACnC,yBAAmB;AACnB,aAAO,CAAC,uBAAuB,GAAiB,MAAM;AAAA,IACxD;AAAA,EACF;AACF;AAEA,SAAS,WACP,QACA,SACA;AACA,SAAO,YAAY,KAAK,MAAM,EAAE,QAAQ,QAAQ,CAAC;AACnD;AAEO,SAAS,SAId;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AACF,GAwCG;AAED,QAAM,iBAAuC,CAAC;AAC9C,aAAWC,SAAQ,SAAS;AAC1B,mBAAeA,KAAI,IAAI,WAAW,QAAQA,KAAI,GAAG;AAAA,MAC/C;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,qBAAqB,eACvB,WAAW,cAAc,CAAC,CAAC,IAC3B;AAEJ,QAAM,KAA4C,OAAM,UAAS;AAhHnE,QAAAC,KAAA;AAiHI,QAAI,cAAc,OAAO;AAIvB,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,QAAI,WAAUA,MAAA,MAAM,mBAAN,OAAAA,MAAwB;AACtC,QAAI,WAAU,WAAM,mBAAN,YAAwB;AACtC,QAAI,eAAe;AAEnB,QAAI,oBAAoB;AACtB,YAAM,CAAC,iBAAiB,UAAU,IAAI,MAAM,mBAAmB,OAAO;AACtE,UAAI,eAAe,QAAW;AAC5B,uBAAe;AACf,kBAAU;AAAA,MACZ;AAAA,IACF;AAEA,WACE;AAAA,MAAC;AAAA;AAAA,QACC;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,QAEpB,gBAAM;AAAA;AAAA,IACT;AAAA,EAEJ;AAEA,SAAO;AACT;;;ACnJA,SAAS,qBAAqB;;;ACD9B,SAAS,kBAAkB;AAE3B,IAAM,OAAO;AACb,IAAM,SAAS,mBAAmB,IAAI;AACtC,IAAM,SAAS,OAAO,IAAI,MAAM;AAJhC;AAMO,IAAM,gBAAN,cAA4B,WAAW;AAAA,EAO5C,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,SAAS,OACf,sBAAsB,GAAG,KAAK,UAAU,IAAI,UAAU,KACtD,sBAAsB,GAAG,KAAK,KAAK;AAAA,EACzC,GAMG;AACD,UAAM,EAAE,MAAM,SAAS,MAAM,CAAC;AArBhC,SAAkB,MAAU;AAuB1B,SAAK,MAAM;AACX,SAAK,aAAa;AAClB,SAAK,aAAa;AAAA,EACpB;AAAA,EAEA,OAAO,WAAW,OAAwC;AACxD,WAAO,WAAW,UAAU,OAAO,MAAM;AAAA,EAC3C;AACF;AA/BoB;;;ACLpB,eAAsB,SAAS,EAAE,IAAI,GAGlC;AALH,MAAAC;AAME,QAAM,UAAU,IAAI,SAAS;AAC7B,MAAI;AACF,UAAM,WAAW,MAAM,MAAM,OAAO;AAEpC,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,IAAI,cAAc;AAAA,QACtB,KAAK;AAAA,QACL,YAAY,SAAS;AAAA,QACrB,YAAY,SAAS;AAAA,MACvB,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,MACL,MAAM,IAAI,WAAW,MAAM,SAAS,YAAY,CAAC;AAAA,MACjD,WAAUA,MAAA,SAAS,QAAQ,IAAI,cAAc,MAAnC,OAAAA,MAAwC;AAAA,IACpD;AAAA,EACF,SAAS,OAAO;AACd,QAAI,cAAc,WAAW,KAAK,GAAG;AACnC,YAAM;AAAA,IACR;AAEA,UAAM,IAAI,cAAc,EAAE,KAAK,SAAS,OAAO,MAAM,CAAC;AAAA,EACxD;AACF;;;AC7BA,SAAS,iCAAiC;AAEnC,IAAM,0BAA0B;AAAA,EACrC;AAAA,IACE,UAAU;AAAA,IACV,aAAa,CAAC,IAAM,IAAM,EAAI;AAAA,IAC9B,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,UAAU;AAAA,IACV,aAAa,CAAC,KAAM,IAAM,IAAM,EAAI;AAAA,IACpC,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,UAAU;AAAA,IACV,aAAa,CAAC,KAAM,GAAI;AAAA,IACxB,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,UAAU;AAAA,IACV,aAAa,CAAC,IAAM,IAAM,IAAM,EAAI;AAAA,IACpC,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,UAAU;AAAA,IACV,aAAa,CAAC,IAAM,EAAI;AAAA,IACxB,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,UAAU;AAAA,IACV,aAAa,CAAC,IAAM,IAAM,IAAM,CAAI;AAAA,IACpC,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,UAAU;AAAA,IACV,aAAa,CAAC,IAAM,IAAM,GAAM,EAAI;AAAA,IACpC,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,UAAU;AAAA,IACV,aAAa;AAAA,MACX;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,IACpE;AAAA,IACA,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,UAAU;AAAA,IACV,aAAa;AAAA,MACX;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,IACpE;AAAA,IACA,cAAc;AAAA,EAChB;AACF;AAmCA,IAAM,WAAW,CAAC,SAA8B;AAC9C,QAAM,QACJ,OAAO,SAAS,WAAW,0BAA0B,IAAI,IAAI;AAC/D,QAAM,WACF,MAAM,CAAC,IAAI,QAAS,MACpB,MAAM,CAAC,IAAI,QAAS,MACpB,MAAM,CAAC,IAAI,QAAS,IACrB,MAAM,CAAC,IAAI;AAGd,SAAO,MAAM,MAAM,UAAU,EAAE;AACjC;AAEA,SAAS,sBAAsB,MAAgD;AAC7E,QAAM,SACH,OAAO,SAAS,YAAY,KAAK,WAAW,MAAM,KAClD,OAAO,SAAS,YACf,KAAK,SAAS,MACd,KAAK,CAAC,MAAM;AAAA,EACZ,KAAK,CAAC,MAAM;AAAA,EACZ,KAAK,CAAC,MAAM;AAEhB,SAAO,SAAS,SAAS,IAAI,IAAI;AACnC;AAEO,SAAS,eAAe;AAAA,EAC7B;AAAA,EACA;AACF,GAGwD;AACtD,QAAM,gBAAgB,sBAAsB,IAAI;AAEhD,aAAW,aAAa,YAAY;AAClC,QACE,OAAO,kBAAkB,WACrB,cAAc,WAAW,UAAU,YAAY,IAC/C,cAAc,UAAU,UAAU,YAAY,UAC9C,UAAU,YAAY;AAAA,MACpB,CAAC,MAAM,UAAU,cAAc,KAAK,MAAM;AAAA,IAC5C,GACJ;AACA,aAAO,UAAU;AAAA,IACnB;AAAA,EACF;AAEA,SAAO;AACT;;;ACvIA;AAAA,EACE,6BAAAC;AAAA,EACA;AAAA,OACK;;;ACHP,SAAS,cAAAC,mBAAkB;AAE3B,IAAMC,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,0BAAN,cAAsCJ,YAAW;AAAA,EAKtD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA,UAAU,+FAA+F,OAAO,OAAO;AAAA,EACzH,GAIG;AACD,UAAM,EAAE,MAAAC,OAAM,SAAS,MAAM,CAAC;AAbhC,SAAkBG,OAAU;AAe1B,SAAK,UAAU;AAAA,EACjB;AAAA,EAEA,OAAO,WAAW,OAAkD;AAClE,WAAOJ,YAAW,UAAU,OAAOE,OAAM;AAAA,EAC3C;AACF;AArBoBE,MAAAD;;;ADFpB,SAAS,SAAS;AAUX,IAAM,oBAA4C,EAAE,MAAM;AAAA,EAC/D,EAAE,OAAO;AAAA,EACT,EAAE,WAAW,UAAU;AAAA,EACvB,EAAE,WAAW,WAAW;AAAA,EACxB,EAAE;AAAA;AAAA,IAEA,CAAC,UAAiC;AArBtC,UAAAE,KAAA;AAsBM,oBAAAA,MAAA,WAAW,WAAX,gBAAAA,IAAmB,SAAS,WAA5B,YAAsC;AAAA;AAAA,IACxC,EAAE,SAAS,mBAAmB;AAAA,EAChC;AACF,CAAC;AAQM,SAAS,iCAAiC,SAA8B;AAC7E,MAAI,OAAO,YAAY,UAAU;AAC/B,WAAO;AAAA,EACT;AAEA,MAAI,mBAAmB,aAAa;AAClC,WAAO,0BAA0B,IAAI,WAAW,OAAO,CAAC;AAAA,EAC1D;AAEA,SAAO,0BAA0B,OAAO;AAC1C;AAQO,SAAS,+BACd,SACY;AACZ,MAAI,mBAAmB,YAAY;AACjC,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,YAAY,UAAU;AAC/B,QAAI;AACF,aAAOC,2BAA0B,OAAO;AAAA,IAC1C,SAAS,OAAO;AACd,YAAM,IAAI,wBAAwB;AAAA,QAChC,SACE;AAAA,QACF;AAAA,QACA,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,mBAAmB,aAAa;AAClC,WAAO,IAAI,WAAW,OAAO;AAAA,EAC/B;AAEA,QAAM,IAAI,wBAAwB,EAAE,QAAQ,CAAC;AAC/C;AAQO,SAAS,wBAAwB,YAAgC;AACtE,MAAI;AACF,WAAO,IAAI,YAAY,EAAE,OAAO,UAAU;AAAA,EAC5C,SAAS,OAAO;AACd,UAAM,IAAI,MAAM,mCAAmC;AAAA,EACrD;AACF;;;AE1FA,SAAS,cAAAC,mBAAkB;AAE3B,IAAMC,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,0BAAN,cAAsCJ,YAAW;AAAA,EAKtD,YAAY;AAAA,IACV;AAAA,IACA,UAAU,0BAA0B,IAAI;AAAA,EAC1C,GAGG;AACD,UAAM,EAAE,MAAAC,OAAM,QAAQ,CAAC;AAXzB,SAAkBG,OAAU;AAa1B,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,OAAO,WAAW,OAAkD;AAClE,WAAOJ,YAAW,UAAU,OAAOE,OAAM;AAAA,EAC3C;AACF;AAnBoBE,MAAAD;;;ACPb,SAAS,aAAa,SAG3B;AACA,MAAI;AACF,UAAM,CAAC,QAAQ,aAAa,IAAI,QAAQ,MAAM,GAAG;AACjD,WAAO;AAAA,MACL,UAAU,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC;AAAA,MAC3C;AAAA,IACF;AAAA,EACF,SAAS,OAAO;AACd,WAAO;AAAA,MACL,UAAU;AAAA,MACV,eAAe;AAAA,IACjB;AAAA,EACF;AACF;;;ACOA,eAAsB,6BAA6B;AAAA,EACjD;AAAA,EACA,yBAAyB;AAAA,EACzB,mBAAmB,MAAM;AAAA,EACzB,yBAAyB;AAC3B,GAKmC;AACjC,QAAM,mBAAmB,MAAM;AAAA,IAC7B,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,SAAO;AAAA,IACL,GAAI,OAAO,UAAU,OACjB,CAAC,EAAE,MAAM,UAAmB,SAAS,OAAO,OAAO,CAAC,IACpD,CAAC;AAAA,IACL,GAAG,OAAO,SAAS;AAAA,MAAI,aACrB,8BAA8B,SAAS,gBAAgB;AAAA,IACzD;AAAA,EACF;AACF;AASO,SAAS,8BACd,SACA,kBAIwB;AAhE1B,MAAAE,KAAA;AAiEE,QAAM,OAAO,QAAQ;AACrB,UAAQ,MAAM;AAAA,IACZ,KAAK,UAAU;AACb,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,QAAQ;AAAA,QACjB,mBACEA,MAAA,QAAQ,oBAAR,OAAAA,MAA2B,QAAQ;AAAA,MACvC;AAAA,IACF;AAAA,IAEA,KAAK,QAAQ;AACX,UAAI,OAAO,QAAQ,YAAY,UAAU;AACvC,eAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS,CAAC,EAAE,MAAM,QAAQ,MAAM,QAAQ,QAAQ,CAAC;AAAA,UACjD,mBACE,aAAQ,oBAAR,YAA2B,QAAQ;AAAA,QACvC;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,QAAQ,QACd,IAAI,UAAQ,+BAA+B,MAAM,gBAAgB,CAAC,EAElE,OAAO,UAAQ,KAAK,SAAS,UAAU,KAAK,SAAS,EAAE;AAAA,QAC1D,mBACE,aAAQ,oBAAR,YAA2B,QAAQ;AAAA,MACvC;AAAA,IACF;AAAA,IAEA,KAAK,aAAa;AAChB,UAAI,OAAO,QAAQ,YAAY,UAAU;AACvC,eAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS,CAAC,EAAE,MAAM,QAAQ,MAAM,QAAQ,QAAQ,CAAC;AAAA,UACjD,mBACE,aAAQ,oBAAR,YAA2B,QAAQ;AAAA,QACvC;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,QAAQ,QACd;AAAA;AAAA,UAEC,UAAQ,KAAK,SAAS,UAAU,KAAK,SAAS;AAAA,QAChD,EACC,IAAI,UAAQ;AAlHvB,cAAAA;AAmHY,gBAAM,mBACJA,OAAA,KAAK,oBAAL,OAAAA,OAAwB,KAAK;AAE/B,kBAAQ,KAAK,MAAM;AAAA,YACjB,KAAK,QAAQ;AACX,qBAAO;AAAA,gBACL,MAAM;AAAA,gBACN,MACE,KAAK,gBAAgB,MACjB,KAAK,OACL,iCAAiC,KAAK,IAAI;AAAA,gBAChD,UAAU,KAAK;AAAA,gBACf,UAAU,KAAK;AAAA,gBACf,kBAAkB;AAAA,cACpB;AAAA,YACF;AAAA,YACA,KAAK,aAAa;AAChB,qBAAO;AAAA,gBACL,MAAM;AAAA,gBACN,MAAM,KAAK;AAAA,gBACX,WAAW,KAAK;AAAA,gBAChB,kBAAkB;AAAA,cACpB;AAAA,YACF;AAAA,YACA,KAAK,sBAAsB;AACzB,qBAAO;AAAA,gBACL,MAAM;AAAA,gBACN,MAAM,KAAK;AAAA,gBACX,kBAAkB;AAAA,cACpB;AAAA,YACF;AAAA,YACA,KAAK,QAAQ;AACX,qBAAO;AAAA,gBACL,MAAM;AAAA,gBACN,MAAM,KAAK;AAAA,gBACX,kBAAkB;AAAA,cACpB;AAAA,YACF;AAAA,YACA,KAAK,aAAa;AAChB,qBAAO;AAAA,gBACL,MAAM;AAAA,gBACN,YAAY,KAAK;AAAA,gBACjB,UAAU,KAAK;AAAA,gBACf,MAAM,KAAK;AAAA,gBACX,kBAAkB;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,QACH,mBACE,aAAQ,oBAAR,YAA2B,QAAQ;AAAA,MACvC;AAAA,IACF;AAAA,IAEA,KAAK,QAAQ;AACX,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,QAAQ,QAAQ,IAAI,UAAK;AA5K1C,cAAAA;AA4K8C;AAAA,YACpC,MAAM;AAAA,YACN,YAAY,KAAK;AAAA,YACjB,UAAU,KAAK;AAAA,YACf,QAAQ,KAAK;AAAA,YACb,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,mBACEA,OAAA,KAAK,oBAAL,OAAAA,OAAwB,KAAK;AAAA,UACjC;AAAA,SAAE;AAAA,QACF,mBACE,aAAQ,oBAAR,YAA2B,QAAQ;AAAA,MACvC;AAAA,IACF;AAAA,IAEA,SAAS;AACP,YAAM,mBAA0B;AAChC,YAAM,IAAI,wBAAwB,EAAE,MAAM,iBAAiB,CAAC;AAAA,IAC9D;AAAA,EACF;AACF;AAKA,eAAe,eACb,UACA,wBACA,wBACA,kBAC6E;AAC7E,QAAM,OAAO,SACV,OAAO,aAAW,QAAQ,SAAS,MAAM,EACzC,IAAI,aAAW,QAAQ,OAAO,EAC9B;AAAA,IAAO,CAAC,YACP,MAAM,QAAQ,OAAO;AAAA,EACvB,EACC,KAAK,EACL;AAAA,IACC,CAAC,SACC,KAAK,SAAS,WAAW,KAAK,SAAS;AAAA,EAC3C,EAKC;AAAA,IACC,CAAC,SACC,EAAE,KAAK,SAAS,WAAW,2BAA2B;AAAA,EAC1D,EACC,IAAI,UAAS,KAAK,SAAS,UAAU,KAAK,QAAQ,KAAK,IAAK,EAC5D;AAAA,IAAI;AAAA;AAAA,MAEH,OAAO,SAAS,aACf,KAAK,WAAW,OAAO,KAAK,KAAK,WAAW,QAAQ,KACjD,IAAI,IAAI,IAAI,IACZ;AAAA;AAAA,EACN,EACC,OAAO,CAAC,UAAwB,iBAAiB,GAAG,EAIpD,OAAO,SAAO,CAAC,iBAAiB,GAAG,CAAC;AAGvC,QAAM,mBAAmB,MAAM,QAAQ;AAAA,IACrC,KAAK,IAAI,OAAM,SAAQ;AAAA,MACrB;AAAA,MACA,MAAM,MAAM,uBAAuB,EAAE,IAAI,CAAC;AAAA,IAC5C,EAAE;AAAA,EACJ;AAEA,SAAO,OAAO;AAAA,IACZ,iBAAiB,IAAI,CAAC,EAAE,KAAK,KAAK,MAAM,CAAC,IAAI,SAAS,GAAG,IAAI,CAAC;AAAA,EAChE;AACF;AAUA,SAAS,+BACP,MACA,kBAO0B;AA1Q5B,MAAAA,KAAA;AA2QE,MAAI,KAAK,SAAS,QAAQ;AACxB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,MAAM,KAAK;AAAA,MACX,mBACEA,MAAA,KAAK,oBAAL,OAAAA,MAAwB,KAAK;AAAA,IACjC;AAAA,EACF;AAEA,MAAI,WAA+B,KAAK;AACxC,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,QAAM,OAAO,KAAK;AAClB,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO,KAAK;AACZ;AAAA,IACF,KAAK;AACH,aAAO,KAAK;AACZ;AAAA,IACF;AACE,YAAM,IAAI,MAAM,0BAA0B,IAAI,EAAE;AAAA,EACpD;AAIA,MAAI;AACF,cAAU,OAAO,SAAS,WAAW,IAAI,IAAI,IAAI,IAAI;AAAA,EACvD,SAAS,OAAO;AACd,cAAU;AAAA,EACZ;AAKA,MAAI,mBAAmB,KAAK;AAE1B,QAAI,QAAQ,aAAa,SAAS;AAChC,YAAM,EAAE,UAAU,iBAAiB,cAAc,IAAI;AAAA,QACnD,QAAQ,SAAS;AAAA,MACnB;AAEA,UAAI,mBAAmB,QAAQ,iBAAiB,MAAM;AACpD,cAAM,IAAI,MAAM,mCAAmC,IAAI,EAAE;AAAA,MAC3D;AAEA,iBAAW;AACX,uBAAiB,+BAA+B,aAAa;AAAA,IAC/D,OAAO;AAML,YAAM,iBAAiB,iBAAiB,QAAQ,SAAS,CAAC;AAC1D,UAAI,gBAAgB;AAClB,yBAAiB,eAAe;AAChC,iDAAa,eAAe;AAAA,MAC9B,OAAO;AACL,yBAAiB;AAAA,MACnB;AAAA,IACF;AAAA,EACF,OAAO;AAGL,qBAAiB,+BAA+B,OAAO;AAAA,EACzD;AAIA,UAAQ,MAAM;AAAA,IACZ,KAAK,SAAS;AAKZ,UAAI,0BAA0B,YAAY;AACxC,oBACE,oBAAe;AAAA,UACb,MAAM;AAAA,UACN,YAAY;AAAA,QACd,CAAC,MAHD,YAGM;AAAA,MACV;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP;AAAA,QACA,mBACE,UAAK,oBAAL,YAAwB,KAAK;AAAA,MACjC;AAAA,IACF;AAAA,IAEA,KAAK,QAAQ;AAEX,UAAI,YAAY,MAAM;AACpB,cAAM,IAAI,MAAM,oCAAoC;AAAA,MACtD;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MACE,0BAA0B,aACtB,iCAAiC,cAAc,IAC/C;AAAA,QACN,UAAU,KAAK;AAAA,QACf;AAAA,QACA,mBACE,UAAK,oBAAL,YAAwB,KAAK;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACF;;;AC5XA,SAAS,cAAAC,mBAAkB;AAE3B,IAAMC,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,uBAAN,cAAmCJ,YAAW;AAAA,EAMnD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAIG;AACD,UAAM;AAAA,MACJ,MAAAC;AAAA,MACA,SAAS,kCAAkC,SAAS,KAAK,OAAO;AAAA,IAClE,CAAC;AAjBH,SAAkBG,OAAU;AAmB1B,SAAK,YAAY;AACjB,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,OAAO,WAAW,OAA+C;AAC/D,WAAOJ,YAAW,UAAU,OAAOE,OAAM;AAAA,EAC3C;AACF;AA1BoBE,MAAAD;;;ACDb,SAAS,oBAAoB;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAGE;AACA,MAAI,aAAa,MAAM;AACrB,QAAI,CAAC,OAAO,UAAU,SAAS,GAAG;AAChC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,YAAY,GAAG;AACjB,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,eAAe,MAAM;AACvB,QAAI,OAAO,gBAAgB,UAAU;AACnC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,QAAQ,MAAM;AAChB,QAAI,OAAO,SAAS,UAAU;AAC5B,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,QAAQ,MAAM;AAChB,QAAI,OAAO,SAAS,UAAU;AAC5B,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,mBAAmB,MAAM;AAC3B,QAAI,OAAO,oBAAoB,UAAU;AACvC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,oBAAoB,MAAM;AAC5B,QAAI,OAAO,qBAAqB,UAAU;AACxC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,QAAQ,MAAM;AAChB,QAAI,CAAC,OAAO,UAAU,IAAI,GAAG;AAC3B,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA;AAAA,IAEA,aAAa,oCAAe;AAAA,IAC5B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,eACE,iBAAiB,QAAQ,cAAc,SAAS,IAC5C,gBACA;AAAA,IACN;AAAA,EACF;AACF;;;AC/GA,SAAS,oBAAoB;AAC7B,SAAS,OAAO,iBAAiB,oBAAoB;;;ACDrD,SAAS,cAAAE,mBAAkB;AAE3B,IAAMC,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAWO,IAAM,aAAN,cAAyBJ,YAAW;AAAA,EAQzC,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAIG;AACD,UAAM,EAAE,MAAAC,OAAM,QAAQ,CAAC;AAhBzB,SAAkBG,OAAU;AAkB1B,SAAK,SAAS;AACd,SAAK,SAAS;AAGd,SAAK,YAAY,OAAO,OAAO,SAAS,CAAC;AAAA,EAC3C;AAAA,EAEA,OAAO,WAAW,OAAqC;AACrD,WAAOJ,YAAW,UAAU,OAAOE,OAAM;AAAA,EAC3C;AACF;AA5BoBE,MAAAD;;;ADAb,IAAM,8BACX,CAAC;AAAA,EACC,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,gBAAgB;AAClB,IAAI,CAAC,MACL,OAAe,MACb,6BAA6B,GAAG;AAAA,EAC9B;AAAA,EACA,WAAW;AAAA,EACX;AACF,CAAC;AAEL,eAAe,6BACb,GACA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AACF,GACA,SAAoB,CAAC,GACJ;AACjB,MAAI;AACF,WAAO,MAAM,EAAE;AAAA,EACjB,SAAS,OAAO;AACd,QAAI,aAAa,KAAK,GAAG;AACvB,YAAM;AAAA,IACR;AAEA,QAAI,eAAe,GAAG;AACpB,YAAM;AAAA,IACR;AAEA,UAAM,eAAe,gBAAgB,KAAK;AAC1C,UAAM,YAAY,CAAC,GAAG,QAAQ,KAAK;AACnC,UAAM,YAAY,UAAU;AAE5B,QAAI,YAAY,YAAY;AAC1B,YAAM,IAAI,WAAW;AAAA,QACnB,SAAS,gBAAgB,SAAS,0BAA0B,YAAY;AAAA,QACxE,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAEA,QACE,iBAAiB,SACjB,aAAa,WAAW,KAAK,KAC7B,MAAM,gBAAgB,QACtB,aAAa,YACb;AACA,YAAM,MAAM,SAAS;AACrB,aAAO;AAAA,QACL;AAAA,QACA,EAAE,YAAY,WAAW,gBAAgB,WAAW,cAAc;AAAA,QAClE;AAAA,MACF;AAAA,IACF;AAEA,QAAI,cAAc,GAAG;AACnB,YAAM;AAAA,IACR;AAEA,UAAM,IAAI,WAAW;AAAA,MACnB,SAAS,gBAAgB,SAAS,wCAAwC,YAAY;AAAA,MACtF,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACF;;;AExEO,SAAS,eAAe;AAAA,EAC7B;AACF,GAKE;AACA,MAAI,cAAc,MAAM;AACtB,QAAI,CAAC,OAAO,UAAU,UAAU,GAAG;AACjC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,aAAa,GAAG;AAClB,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM,mBAAmB,kCAAc;AAEvC,SAAO;AAAA,IACL,YAAY;AAAA,IACZ,OAAO,4BAA4B,EAAE,YAAY,iBAAiB,CAAC;AAAA,EACrE;AACF;;;ACpCA,SAAS,gBAAgB;;;ACLlB,SAAS,iBACd,QACmC;AACnC,SAAO,UAAU,QAAQ,OAAO,KAAK,MAAM,EAAE,SAAS;AACxD;;;ADMO,SAAS,0BAAiD;AAAA,EAC/D;AAAA,EACA;AAAA,EACA;AACF,GASE;AACA,MAAI,CAAC,iBAAiB,KAAK,GAAG;AAC5B,WAAO;AAAA,MACL,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,EACF;AAGA,QAAM,gBACJ,eAAe,OACX,OAAO,QAAQ,KAAK,EAAE;AAAA,IAAO,CAAC,CAACE,KAAI,MACjC,YAAY,SAASA,KAAmB;AAAA,EAC1C,IACA,OAAO,QAAQ,KAAK;AAE1B,SAAO;AAAA,IACL,OAAO,cAAc,IAAI,CAAC,CAACA,OAAM,IAAI,MAAM;AACzC,YAAM,WAAW,KAAK;AACtB,cAAQ,UAAU;AAAA,QAChB,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,MAAAA;AAAA,YACA,aAAa,KAAK;AAAA,YAClB,YAAY,SAAS,KAAK,UAAU,EAAE;AAAA,UACxC;AAAA,QACF,KAAK;AACH,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,MAAAA;AAAA,YACA,IAAI,KAAK;AAAA,YACT,MAAM,KAAK;AAAA,UACb;AAAA,QACF,SAAS;AACP,gBAAM,kBAAyB;AAC/B,gBAAM,IAAI,MAAM,0BAA0B,eAAe,EAAE;AAAA,QAC7D;AAAA,MACF;AAAA,IACF,CAAC;AAAA,IACD,YACE,cAAc,OACV,EAAE,MAAM,OAAO,IACf,OAAO,eAAe,WACpB,EAAE,MAAM,WAAW,IACnB,EAAE,MAAM,QAAiB,UAAU,WAAW,SAAmB;AAAA,EAC3E;AACF;;;AEvEA,SAAS,0BAA0B;AACnC,SAAS,yBAAyB;AAElC,SAAS,KAAAC,UAAS;;;ACWX,SAAS,mBAAmB,aAA0C;AAd7E,MAAAC,KAAA;AAeE,QAAM,QAAuB,CAAC;AAE9B,aAAW,cAAc,aAAa;AACpC,QAAI;AAEJ,QAAI;AACF,YAAM,IAAI,IAAI,WAAW,GAAG;AAAA,IAC9B,SAAS,OAAO;AACd,YAAM,IAAI,MAAM,gBAAgB,WAAW,GAAG,EAAE;AAAA,IAClD;AAEA,YAAQ,IAAI,UAAU;AAAA,MACpB,KAAK;AAAA,MACL,KAAK,UAAU;AACb,aAAIA,MAAA,WAAW,gBAAX,gBAAAA,IAAwB,WAAW,WAAW;AAChD,gBAAM,KAAK,EAAE,MAAM,SAAS,OAAO,IAAI,CAAC;AAAA,QAC1C,OAAO;AACL,cAAI,CAAC,WAAW,aAAa;AAC3B,kBAAM,IAAI;AAAA,cACR;AAAA,YACF;AAAA,UACF;AAEA,gBAAM,KAAK;AAAA,YACT,MAAM;AAAA,YACN,MAAM;AAAA,YACN,UAAU,WAAW;AAAA,UACvB,CAAC;AAAA,QACH;AACA;AAAA,MACF;AAAA,MAEA,KAAK,SAAS;AACZ,YAAI;AACJ,YAAI;AACJ,YAAI;AAEJ,YAAI;AACF,WAAC,QAAQ,aAAa,IAAI,WAAW,IAAI,MAAM,GAAG;AAClD,qBAAW,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC;AAAA,QAC9C,SAAS,OAAO;AACd,gBAAM,IAAI,MAAM,8BAA8B,WAAW,GAAG,EAAE;AAAA,QAChE;AAEA,YAAI,YAAY,QAAQ,iBAAiB,MAAM;AAC7C,gBAAM,IAAI,MAAM,4BAA4B,WAAW,GAAG,EAAE;AAAA,QAC9D;AAEA,aAAI,gBAAW,gBAAX,mBAAwB,WAAW,WAAW;AAChD,gBAAM,KAAK;AAAA,YACT,MAAM;AAAA,YACN,OAAO,+BAA+B,aAAa;AAAA,UACrD,CAAC;AAAA,QACH,YAAW,gBAAW,gBAAX,mBAAwB,WAAW,UAAU;AACtD,gBAAM,KAAK;AAAA,YACT,MAAM;AAAA,YACN,MAAM;AAAA,cACJ,+BAA+B,aAAa;AAAA,YAC9C;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,cAAI,CAAC,WAAW,aAAa;AAC3B,kBAAM,IAAI;AAAA,cACR;AAAA,YACF;AAAA,UACF;AAEA,gBAAM,KAAK;AAAA,YACT,MAAM;AAAA,YACN,MAAM;AAAA,YACN,UAAU,WAAW;AAAA,UACvB,CAAC;AAAA,QACH;AAEA;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,IAAI,MAAM,6BAA6B,IAAI,QAAQ,EAAE;AAAA,MAC7D;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;ACnGA,SAAS,cAAAC,mBAAkB;AAG3B,IAAMC,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AALhC,IAAAE;AAOO,IAAM,yBAAN,cAAqCJ,YAAW;AAAA,EAKrD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAGG;AACD,UAAM,EAAE,MAAAC,OAAM,QAAQ,CAAC;AAXzB,SAAkBG,OAAU;AAa1B,SAAK,kBAAkB;AAAA,EACzB;AAAA,EAEA,OAAO,WAAW,OAAiD;AACjE,WAAOJ,YAAW,UAAU,OAAOE,OAAM;AAAA,EAC3C;AACF;AAnBoBE,MAAAD;;;ACab,SAAS,sBACd,UACA,SACA;AAxBF,MAAAE,KAAA;AAyBE,QAAM,SAAQA,MAAA,mCAAS,UAAT,OAAAA,MAAmB,CAAC;AAClC,QAAM,eAA8B,CAAC;AAErC,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAM,UAAU,SAAS,CAAC;AAC1B,UAAM,gBAAgB,MAAM,SAAS,SAAS;AAC9C,UAAM,EAAE,MAAM,SAAS,yBAAyB,IAAI;AAEpD,YAAQ,MAAM;AAAA,MACZ,KAAK,UAAU;AACb,qBAAa,KAAK;AAAA,UAChB,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AACD;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AACX,YAAI,QAAQ,SAAS,MAAM;AACzB,uBAAa,KAAK;AAAA,YAChB,MAAM;AAAA,YACN,SAAS,2BACL;AAAA,cACE,EAAE,MAAM,QAAQ,MAAM,QAAQ;AAAA,cAC9B,GAAG,mBAAmB,wBAAwB;AAAA,YAChD,IACA;AAAA,UACN,CAAC;AAAA,QACH,OAAO;AACL,gBAAM,YAAY,QAAQ,MACvB,OAAO,UAAQ,KAAK,SAAS,MAAM,EACnC,IAAI,WAAS;AAAA,YACZ,MAAM;AAAA,YACN,MAAM,KAAK;AAAA,UACb,EAAE;AAEJ,uBAAa,KAAK;AAAA,YAChB,MAAM;AAAA,YACN,SAAS,2BACL,CAAC,GAAG,WAAW,GAAG,mBAAmB,wBAAwB,CAAC,IAC9D;AAAA,UACN,CAAC;AAAA,QACH;AACA;AAAA,MACF;AAAA,MAEA,KAAK,aAAa;AAChB,YAAI,QAAQ,SAAS,MAAM;AAOzB,cAASC,gBAAT,WAAwB;AACtB,kBAAMC,WAA4B,CAAC;AAEnC,uBAAW,QAAQ,OAAO;AACxB,sBAAQ,KAAK,MAAM;AAAA,gBACjB,KAAK;AAAA,gBACL,KAAK,QAAQ;AACX,kBAAAA,SAAQ,KAAK,IAAI;AACjB;AAAA,gBACF;AAAA,gBACA,KAAK,aAAa;AAChB,6BAAW,UAAU,KAAK,SAAS;AACjC,4BAAQ,OAAO,MAAM;AAAA,sBACnB,KAAK;AACH,wBAAAA,SAAQ,KAAK;AAAA,0BACX,MAAM;AAAA,0BACN,MAAM,OAAO;AAAA,0BACb,WAAW,OAAO;AAAA,wBACpB,CAAC;AACD;AAAA,sBACF,KAAK;AACH,wBAAAA,SAAQ,KAAK;AAAA,0BACX,MAAM;AAAA,0BACN,MAAM,OAAO;AAAA,wBACf,CAAC;AACD;AAAA,oBACJ;AAAA,kBACF;AACA;AAAA,gBACF;AAAA,gBACA,KAAK;AACH,kBAAAA,SAAQ,KAAK;AAAA,oBACX,MAAM;AAAA,oBACN,YAAY,KAAK,eAAe;AAAA,oBAChC,UAAU,KAAK,eAAe;AAAA,oBAC9B,MAAM,KAAK,eAAe;AAAA,kBAC5B,CAAC;AACD;AAAA,gBACF,SAAS;AACP,wBAAM,mBAA0B;AAChC,wBAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,gBACzD;AAAA,cACF;AAAA,YACF;AAEA,yBAAa,KAAK;AAAA,cAChB,MAAM;AAAA,cACN,SAAAA;AAAA,YACF,CAAC;AAGD,kBAAM,kBAAkB,MACrB;AAAA,cACC,CACE,SAMA,KAAK,SAAS;AAAA,YAClB,EACC,IAAI,UAAQ,KAAK,cAAc;AAGlC,gBAAI,gBAAgB,SAAS,GAAG;AAC9B,2BAAa,KAAK;AAAA,gBAChB,MAAM;AAAA,gBACN,SAAS,gBAAgB;AAAA,kBACvB,CAAC,mBAAmC;AAClC,wBAAI,EAAE,YAAY,iBAAiB;AACjC,4BAAM,IAAI,uBAAuB;AAAA,wBAC/B,iBAAiB;AAAA,wBACjB,SACE,wCACA,KAAK,UAAU,cAAc;AAAA,sBACjC,CAAC;AAAA,oBACH;AAEA,0BAAM,EAAE,YAAY,UAAU,OAAO,IAAI;AAEzC,0BAAM,OAAO,MAAM,QAAQ;AAC3B,4BAAO,6BAAM,qCAAoC,OAC7C;AAAA,sBACE,MAAM;AAAA,sBACN;AAAA,sBACA;AAAA,sBACA,QAAQ,KAAK,iCAAiC,MAAM;AAAA,sBACpD,sBACE,KAAK,iCAAiC,MAAM;AAAA,oBAChD,IACA;AAAA,sBACE,MAAM;AAAA,sBACN;AAAA,sBACA;AAAA,sBACA;AAAA,oBACF;AAAA,kBACN;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAGA,oBAAQ,CAAC;AACT,sCAA0B;AAC1B;AAAA,UACF;AA1GS,6BAAAD;AANT,cAAI,cAAc;AAClB,cAAI,0BAA0B;AAC9B,cAAI,QAEA,CAAC;AA8GL,qBAAW,QAAQ,QAAQ,OAAO;AAChC,oBAAQ,KAAK,MAAM;AAAA,cACjB,KAAK,QAAQ;AACX,oBAAI,yBAAyB;AAC3B,kBAAAA,cAAa;AAAA,gBACf;AACA,sBAAM,KAAK,IAAI;AACf;AAAA,cACF;AAAA,cACA,KAAK;AAAA,cACL,KAAK,aAAa;AAChB,sBAAM,KAAK,IAAI;AACf;AAAA,cACF;AAAA,cACA,KAAK,mBAAmB;AACtB,sBAAK,UAAK,eAAe,SAApB,YAA4B,OAAO,aAAa;AACnD,kBAAAA,cAAa;AAAA,gBACf;AACA,sBAAM,KAAK,IAAI;AACf,0CAA0B;AAC1B;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAEA,UAAAA,cAAa;AAEb;AAAA,QACF;AAEA,cAAM,kBAAkB,QAAQ;AAEhC,YAAI,mBAAmB,QAAQ,gBAAgB,WAAW,GAAG;AAC3D,uBAAa,KAAK,EAAE,MAAM,aAAa,QAAQ,CAAC;AAChD;AAAA,QACF;AAEA,cAAM,UAAU,gBAAgB,OAAO,CAAC,KAAK,mBAAmB;AAhOxE,cAAAD;AAiOU,iBAAO,KAAK,IAAI,MAAKA,OAAA,eAAe,SAAf,OAAAA,OAAuB,CAAC;AAAA,QAC/C,GAAG,CAAC;AAEJ,iBAASG,KAAI,GAAGA,MAAK,SAASA,MAAK;AACjC,gBAAM,kBAAkB,gBAAgB;AAAA,YACtC,oBAAe;AAtO3B,kBAAAH;AAsO+B,uBAAAA,OAAA,eAAe,SAAf,OAAAA,OAAuB,OAAOG;AAAA;AAAA,UACnD;AAEA,cAAI,gBAAgB,WAAW,GAAG;AAChC;AAAA,UACF;AAGA,uBAAa,KAAK;AAAA,YAChB,MAAM;AAAA,YACN,SAAS;AAAA,cACP,GAAI,iBAAiB,WAAWA,OAAM,IAClC,CAAC,EAAE,MAAM,QAAiB,MAAM,QAAQ,CAAC,IACzC,CAAC;AAAA,cACL,GAAG,gBAAgB;AAAA,gBACjB,CAAC,EAAE,YAAY,UAAU,KAAK,OAAqB;AAAA,kBACjD,MAAM;AAAA,kBACN;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAC;AAGD,uBAAa,KAAK;AAAA,YAChB,MAAM;AAAA,YACN,SAAS,gBAAgB,IAAI,CAAC,mBAAmC;AAC/D,kBAAI,EAAE,YAAY,iBAAiB;AACjC,sBAAM,IAAI,uBAAuB;AAAA,kBAC/B,iBAAiB;AAAA,kBACjB,SACE,wCACA,KAAK,UAAU,cAAc;AAAA,gBACjC,CAAC;AAAA,cACH;AAEA,oBAAM,EAAE,YAAY,UAAU,OAAO,IAAI;AAEzC,oBAAM,OAAO,MAAM,QAAQ;AAC3B,sBAAO,6BAAM,qCAAoC,OAC7C;AAAA,gBACE,MAAM;AAAA,gBACN;AAAA,gBACA;AAAA,gBACA,QAAQ,KAAK,iCAAiC,MAAM;AAAA,gBACpD,sBACE,KAAK,iCAAiC,MAAM;AAAA,cAChD,IACA;AAAA,gBACE,MAAM;AAAA,gBACN;AAAA,gBACA;AAAA,gBACA;AAAA,cACF;AAAA,YACN,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAEA,YAAI,WAAW,CAAC,eAAe;AAC7B,uBAAa,KAAK,EAAE,MAAM,aAAa,QAAQ,CAAC;AAAA,QAClD;AAEA;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AAEX;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,uBAAuB;AAAA,UAC/B,iBAAiB;AAAA,UACjB,SAAS,qBAAqB,gBAAgB;AAAA,QAChD,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;ACzTA,SAAS,KAAAC,UAAS;;;ACClB,SAAS,KAAAC,UAAS;;;ACAlB,SAAS,KAAAC,UAAS;AAEX,IAAM,kBAAwCA,GAAE;AAAA,EAAK,MAC1DA,GAAE,MAAM;AAAA,IACNA,GAAE,KAAK;AAAA,IACPA,GAAE,OAAO;AAAA,IACTA,GAAE,OAAO;AAAA,IACTA,GAAE,QAAQ;AAAA,IACVA,GAAE,OAAOA,GAAE,OAAO,GAAG,eAAe;AAAA,IACpCA,GAAE,MAAM,eAAe;AAAA,EACzB,CAAC;AACH;;;ADSO,IAAM,yBAAsDC,GAAE;AAAA,EACnEA,GAAE,OAAO;AAAA,EACTA,GAAE,OAAOA,GAAE,OAAO,GAAG,eAAe;AACtC;;;AExBA,SAAS,KAAAC,UAAS;;;ACAlB,SAAS,KAAAC,UAAS;AAcX,IAAM,0BAAwDA,GAAE;AAAA,EACrEA,GAAE,MAAM;AAAA,IACNA,GAAE,OAAO,EAAE,MAAMA,GAAE,QAAQ,MAAM,GAAG,MAAMA,GAAE,OAAO,EAAE,CAAC;AAAA,IACtDA,GAAE,OAAO;AAAA,MACP,MAAMA,GAAE,QAAQ,OAAO;AAAA,MACvB,MAAMA,GAAE,OAAO;AAAA,MACf,UAAUA,GAAE,OAAO,EAAE,SAAS;AAAA,IAChC,CAAC;AAAA,EACH,CAAC;AACH;;;ADgBO,IAAM,iBAAsCC,GAAE,OAAO;AAAA,EAC1D,MAAMA,GAAE,QAAQ,MAAM;AAAA,EACtB,MAAMA,GAAE,OAAO;AAAA,EACf,iBAAiB,uBAAuB,SAAS;AAAA,EACjD,+BAA+B,uBAAuB,SAAS;AACjE,CAAC;AAqCM,IAAM,kBAAwCA,GAAE,OAAO;AAAA,EAC5D,MAAMA,GAAE,QAAQ,OAAO;AAAA,EACvB,OAAOA,GAAE,MAAM,CAAC,mBAAmBA,GAAE,WAAW,GAAG,CAAC,CAAC;AAAA,EACrD,UAAUA,GAAE,OAAO,EAAE,SAAS;AAAA,EAC9B,iBAAiB,uBAAuB,SAAS;AAAA,EACjD,+BAA+B,uBAAuB,SAAS;AACjE,CAAC;AA0CM,IAAM,iBAAsCA,GAAE,OAAO;AAAA,EAC1D,MAAMA,GAAE,QAAQ,MAAM;AAAA,EACtB,MAAMA,GAAE,MAAM,CAAC,mBAAmBA,GAAE,WAAW,GAAG,CAAC,CAAC;AAAA,EACpD,UAAUA,GAAE,OAAO,EAAE,SAAS;AAAA,EAC9B,UAAUA,GAAE,OAAO;AAAA,EACnB,iBAAiB,uBAAuB,SAAS;AAAA,EACjD,+BAA+B,uBAAuB,SAAS;AACjE,CAAC;AAkCM,IAAM,sBAAgDA,GAAE,OAAO;AAAA,EACpE,MAAMA,GAAE,QAAQ,WAAW;AAAA,EAC3B,MAAMA,GAAE,OAAO;AAAA,EACf,iBAAiB,uBAAuB,SAAS;AAAA,EACjD,+BAA+B,uBAAuB,SAAS;AACjE,CAAC;AA6BM,IAAM,8BACXA,GAAE,OAAO;AAAA,EACP,MAAMA,GAAE,QAAQ,oBAAoB;AAAA,EACpC,MAAMA,GAAE,OAAO;AAAA,EACf,iBAAiB,uBAAuB,SAAS;AAAA,EACjD,+BAA+B,uBAAuB,SAAS;AACjE,CAAC;AAuCI,IAAM,qBAA8CA,GAAE,OAAO;AAAA,EAClE,MAAMA,GAAE,QAAQ,WAAW;AAAA,EAC3B,YAAYA,GAAE,OAAO;AAAA,EACrB,UAAUA,GAAE,OAAO;AAAA,EACnB,MAAMA,GAAE,QAAQ;AAAA,EAChB,iBAAiB,uBAAuB,SAAS;AAAA,EACjD,+BAA+B,uBAAuB,SAAS;AACjE,CAAC;AAiDM,IAAM,uBAAkDA,GAAE,OAAO;AAAA,EACtE,MAAMA,GAAE,QAAQ,aAAa;AAAA,EAC7B,YAAYA,GAAE,OAAO;AAAA,EACrB,UAAUA,GAAE,OAAO;AAAA,EACnB,QAAQA,GAAE,QAAQ;AAAA,EAClB,SAAS,wBAAwB,SAAS;AAAA,EAC1C,SAASA,GAAE,QAAQ,EAAE,SAAS;AAAA,EAC9B,iBAAiB,uBAAuB,SAAS;AAAA,EACjD,+BAA+B,uBAAuB,SAAS;AACjE,CAAC;;;AH3QM,IAAM,0BAAwDC,GAAE,OAAO;AAAA,EAC5E,MAAMA,GAAE,QAAQ,QAAQ;AAAA,EACxB,SAASA,GAAE,OAAO;AAAA,EAClB,iBAAiB,uBAAuB,SAAS;AAAA,EACjD,+BAA+B,uBAAuB,SAAS;AACjE,CAAC;AAsBM,IAAM,wBAAoDA,GAAE,OAAO;AAAA,EACxE,MAAMA,GAAE,QAAQ,MAAM;AAAA,EACtB,SAASA,GAAE,MAAM;AAAA,IACfA,GAAE,OAAO;AAAA,IACTA,GAAE,MAAMA,GAAE,MAAM,CAAC,gBAAgB,iBAAiB,cAAc,CAAC,CAAC;AAAA,EACpE,CAAC;AAAA,EACD,iBAAiB,uBAAuB,SAAS;AAAA,EACjD,+BAA+B,uBAAuB,SAAS;AACjE,CAAC;AA2BM,IAAM,6BACXA,GAAE,OAAO;AAAA,EACP,MAAMA,GAAE,QAAQ,WAAW;AAAA,EAC3B,SAASA,GAAE,MAAM;AAAA,IACfA,GAAE,OAAO;AAAA,IACTA,GAAE;AAAA,MACAA,GAAE,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AAAA,EACD,iBAAiB,uBAAuB,SAAS;AAAA,EACjD,+BAA+B,uBAAuB,SAAS;AACjE,CAAC;AAgCI,IAAM,wBAAoDA,GAAE,OAAO;AAAA,EACxE,MAAMA,GAAE,QAAQ,MAAM;AAAA,EACtB,SAASA,GAAE,MAAM,oBAAoB;AAAA,EACrC,iBAAiB,uBAAuB,SAAS;AAAA,EACjD,+BAA+B,uBAAuB,SAAS;AACjE,CAAC;AAiBM,IAAM,oBAA4CA,GAAE,MAAM;AAAA,EAC/D;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;AJ9JM,SAAS,kBAAyC;AAAA,EACvD;AAAA,EACA;AACF,GAGuB;AACrB,MAAI,OAAO,UAAU,QAAQ,OAAO,YAAY,MAAM;AACpD,UAAM,IAAI,mBAAmB;AAAA,MAC3B;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,UAAU,QAAQ,OAAO,YAAY,MAAM;AACpD,UAAM,IAAI,mBAAmB;AAAA,MAC3B;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAGA,MAAI,OAAO,UAAU,QAAQ,OAAO,OAAO,WAAW,UAAU;AAC9D,UAAM,IAAI,mBAAmB;AAAA,MAC3B;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAGA,MAAI,OAAO,UAAU,MAAM;AAEzB,QAAI,OAAO,OAAO,WAAW,UAAU;AACrC,YAAM,IAAI,mBAAmB;AAAA,QAC3B;AAAA,QACA,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN,QAAQ,OAAO;AAAA,MACf,UAAU;AAAA,QACR;AAAA,UACE,MAAM;AAAA,UACN,SAAS,OAAO;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAGA,MAAI,OAAO,YAAY,MAAM;AAC3B,UAAM,aAAa,iBAAiB,OAAO,QAAQ;AAEnD,UAAM,WACJ,eAAe,gBACX,sBAAsB,OAAO,UAAmC;AAAA,MAC9D;AAAA,IACF,CAAC,IACA,OAAO;AAEd,QAAI,SAAS,WAAW,GAAG;AACzB,YAAM,IAAI,mBAAmB;AAAA,QAC3B;AAAA,QACA,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,UAAM,mBAAmB,kBAAkB;AAAA,MACzC,OAAO;AAAA,MACP,QAAQC,GAAE,MAAM,iBAAiB;AAAA,IACnC,CAAC;AAED,QAAI,CAAC,iBAAiB,SAAS;AAC7B,YAAM,IAAI,mBAAmB;AAAA,QAC3B;AAAA,QACA,SAAS;AAAA,UACP;AAAA,UACA,qBAAqB,iBAAiB,MAAM,OAAO;AAAA,QACrD,EAAE,KAAK,IAAI;AAAA,QACX,OAAO,iBAAiB;AAAA,MAC1B,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,QAAQ,OAAO;AAAA,IACjB;AAAA,EACF;AAEA,QAAM,IAAI,MAAM,aAAa;AAC/B;AAEA,SAAS,iBACP,QACsC;AACtC,MAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,UAAM,IAAI,mBAAmB;AAAA,MAC3B;AAAA,MACA,SAAS;AAAA,QACP;AAAA,QACA,6BAA6B,KAAK,UAAU,MAAM,CAAC;AAAA,MACrD,EAAE,KAAK,IAAI;AAAA,MACX,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,WAAW,GAAG;AACvB,WAAO;AAAA,EACT;AAEA,QAAM,kBAAkB,OAAO,IAAI,kCAAkC;AAErE,MAAI,gBAAgB,KAAK,OAAK,MAAM,uBAAuB,GAAG;AAC5D,WAAO;AAAA,EACT;AAEA,QAAM,kBAAkB,gBAAgB;AAAA,IACtC,OAAK,MAAM,6BAA6B,MAAM;AAAA,EAChD;AAEA,MAAI,oBAAoB,IAAI;AAC1B,WAAO;AAAA,EACT;AAEA,QAAM,IAAI,mBAAmB;AAAA,IAC3B;AAAA,IACA,SAAS;AAAA,MACP;AAAA,MACA,8BAA8B,gBAAgB,eAAe,CAAC,cAAc,eAAe;AAAA,MAC3F,YAAY,eAAe,MAAM,KAAK,UAAU,OAAO,eAAe,CAAC,CAAC;AAAA,IAC1E,EAAE,KAAK,IAAI;AAAA,IACX,OAAO;AAAA,EACT,CAAC;AACH;AAEA,SAAS,mCACP,SAC2E;AAC3E,MACE,OAAO,YAAY,YACnB,YAAY,SACX,QAAQ,SAAS;AAAA,EAChB,QAAQ,SAAS;AAAA,EACjB,qBAAqB;AAAA,EACrB,WAAW;AAAA,EACX,8BAA8B,UAChC;AACA,WAAO;AAAA,EACT,WACE,OAAO,YAAY,YACnB,YAAY,QACZ,aAAa,YACZ,MAAM,QAAQ,QAAQ,OAAO;AAAA,EAC5B,mCAAmC,WACnC,qBAAqB,UACvB;AACA,WAAO;AAAA,EACT,WACE,OAAO,YAAY,YACnB,YAAY,QACZ,UAAU,WACV,aAAa,WACb,OAAO,QAAQ,YAAY,YAC3B,CAAC,UAAU,QAAQ,aAAa,MAAM,EAAE,SAAS,QAAQ,IAAI,GAC7D;AACA,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;;;ASzKO,SAAS,4BAA4B;AAAA,EAC1C;AAAA,EACA;AACF,GAGuB;AACrB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,aAAa,eAAe;AAAA,EAC9B;AACF;;;AC1CA,SAAS,cAAAC,aAAY,mBAAAC,wBAAuB;AAE5C,IAAMC,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,4BAAN,cAAwCL,YAAW;AAAA,EAMxD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,8BAA8B,QAAQ,KAAKC;AAAA,MACnD;AAAA,IACF,CAAC;AAAA,EACH,GAKG;AACD,UAAM,EAAE,MAAAC,OAAM,SAAS,MAAM,CAAC;AAlBhC,SAAkBG,OAAU;AAoB1B,SAAK,WAAW;AAChB,SAAK,WAAW;AAAA,EAClB;AAAA,EAEA,OAAO,WAAW,OAAoD;AACpE,WAAOL,YAAW,UAAU,OAAOG,OAAM;AAAA,EAC3C;AACF;AA3BoBE,MAAAD;;;ACPpB,SAAS,cAAAE,mBAAkB;AAE3B,IAAMC,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,kBAAN,cAA8BJ,YAAW;AAAA,EAM9C,YAAY;AAAA,IACV;AAAA,IACA,iBAAiB;AAAA,IACjB,UAAU,yCAAyC,QAAQ,MACzD,mBAAmB,SACf,4BACA,oBAAoB,eAAe,KAAK,IAAI,CAAC,GACnD;AAAA,EACF,GAIG;AACD,UAAM,EAAE,MAAAC,OAAM,QAAQ,CAAC;AAlBzB,SAAkBG,OAAU;AAoB1B,SAAK,WAAW;AAChB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EAEA,OAAO,WAAW,OAA0C;AAC1D,WAAOJ,YAAW,UAAU,OAAOE,OAAM;AAAA,EAC3C;AACF;AA3BoBE,MAAAD;;;ACPb,SAAS,iBACd,OAC4C;AAC5C,SACE,SAAS,QAAQ,OAAO,UAAU,YAAY,OAAO,iBAAiB;AAE1E;;;ACNO,SAAS,YACd,OACuC;AACvC,SAAO,SAAS,QAAQ,OAAO,UAAU,YAAY,OAAO,YAAY;AAC1E;;;ACAO,IAAM,iCAAiC,KAAK;;;ACJnD,SAAgB,gBAAgB;AAmCxB,mBAGI,OAAAE,MAHJ;AAhBR,IAAM,IAAI;AAAA,EACP,OAAO;AAAA,IACN,GAAG;AAAA,IACH,GAAG;AAAA,EACL,MAGM;AACJ,UAAM,QAAQ,MAAM;AAEpB,QAAI,MAAM,MAAM;AACd,aAAO,MAAM;AAAA,IACf;AAEA,QAAI,MAAM,QAAQ;AAChB,aACE,iCACG;AAAA;AAAA,QACD,gBAAAA,KAAC,YAAS,UAAU,MAAM,OACxB,0BAAAA,KAAC,KAAE,GAAG,MAAM,OAAO,GAAG,MAAM,MAAM,GACpC;AAAA,SACF;AAAA,IAEJ;AAEA,WACE,gBAAAA,KAAC,YAAS,UAAU,MAAM,OACxB,0BAAAA,KAAC,KAAE,GAAG,MAAM,OAAO,GAAG,MAAM,MAAM,GACpC;AAAA,EAEJ;AAIF,EAAE,CAAC;AAcI,SAAS,qBAAqB,cAInC;AACA,QAAM,EAAE,SAAS,SAAS,OAAO,IAAI,wBAAmC;AAExE,SAAO;AAAA,IACL,KACE,gBAAAA,KAAC,YAAS,UAAU,cAClB,0BAAAA,KAAC,KAAE,GAAG,cAAc,GAAG,SAAS,GAClC;AAAA,IAEF;AAAA,IACA;AAAA,EACF;AACF;;;AC7BA,SAAS,mBAAmB,cAAgC;AAC1D,MAAI,eAAe;AACnB,MAAI,SAAS;AACb,MAAI,EAAE,KAAK,SAAS,OAAO,IAAI,qBAAqB,YAAY;AAEhE,WAAS,aAAa,QAAgB;AACpC,QAAI,QAAQ;AACV,YAAM,IAAI,MAAM,SAAS,gCAAgC;AAAA,IAC3D;AAAA,EACF;AAEA,MAAI;AACJ,WAAS,qBAAqB;AAC5B,QAAI,QAAQ,IAAI,aAAa,eAAe;AAC1C,UAAI,gBAAgB;AAClB,qBAAa,cAAc;AAAA,MAC7B;AACA,uBAAiB,WAAW,MAAM;AAChC,gBAAQ;AAAA,UACN;AAAA,QACF;AAAA,MACF,GAAG,8BAA8B;AAAA,IACnC;AAAA,EACF;AACA,qBAAmB;AAEnB,QAAM,aAAkC;AAAA,IACtC,OAAO;AAAA,IACP,OAAO,OAAwB;AAC7B,mBAAa,WAAW;AAGxB,UAAI,UAAU,cAAc;AAC1B,2BAAmB;AACnB,eAAO;AAAA,MACT;AAEA,YAAM,aAAa,wBAAwB;AAC3C,qBAAe;AAEf,cAAQ,EAAE,OAAO,cAAc,MAAM,OAAO,MAAM,WAAW,QAAQ,CAAC;AACtE,gBAAU,WAAW;AACrB,eAAS,WAAW;AAEpB,yBAAmB;AAEnB,aAAO;AAAA,IACT;AAAA,IACA,OAAO,OAAwB;AAC7B,mBAAa,WAAW;AAExB,YAAM,aAAa,wBAAwB;AAC3C,qBAAe;AAEf,cAAQ,EAAE,OAAO,MAAM,OAAO,QAAQ,MAAM,MAAM,WAAW,QAAQ,CAAC;AACtE,gBAAU,WAAW;AACrB,eAAS,WAAW;AAEpB,yBAAmB;AAEnB,aAAO;AAAA,IACT;AAAA,IACA,MAAM,OAAY;AAChB,mBAAa,UAAU;AAEvB,UAAI,gBAAgB;AAClB,qBAAa,cAAc;AAAA,MAC7B;AACA,eAAS;AACT,aAAO,KAAK;AAEZ,aAAO;AAAA,IACT;AAAA,IACA,QAAQ,MAA8B;AACpC,mBAAa,SAAS;AAEtB,UAAI,gBAAgB;AAClB,qBAAa,cAAc;AAAA,MAC7B;AACA,eAAS;AACT,UAAI,KAAK,QAAQ;AACf,gBAAQ,EAAE,OAAO,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC;AACtC,eAAO;AAAA,MACT;AACA,cAAQ,EAAE,OAAO,cAAc,MAAM,KAAK,CAAC;AAE3C,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;AhCnEA,IAAM,sBAAkC,CAAC,EAAE,QAAQ,MACjD;AAKF,eAAsB,SAEpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB;AAAA,EAClB;AAAA,EACA,GAAG;AACL,GAgE4B;AAE1B,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,MAAI,eAAe,UAAU;AAC3B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,MAAI,cAAc,UAAU;AAC1B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,MAAI,OAAO;AACT,eAAW,CAACC,OAAM,IAAI,KAAK,OAAO,QAAQ,KAAK,GAAG;AAChD,UAAI,YAAY,MAAM;AACpB,cAAM,IAAI;AAAA,UACR,6GACEA;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,KAAK,mBAAmB,OAAO;AAGrC,QAAM,aAAa,QAAQ;AAE3B,MAAI;AAEJ,MAAI,cAOO;AAEX,iBAAe,OAAO;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,EACf,GAKG;AACD,QAAI,CAAC;AAAU;AAKf,UAAM,iBAAiB,wBAA8B;AACrD,eAAW,WACP,SAAS,KAAK,MAAM,eAAe,OAAO,IAC1C,eAAe;AAEnB,UAAM,iBAAiB,SAAS,GAAG,IAAI;AAEvC,QAAI,iBAAiB,cAAc,KAAK,YAAY,cAAc,GAAG;AACnE,aAAO,MAAM;AACX,cAAM,EAAE,MAAM,MAAM,IAAI,MAAM,eAAe,KAAK;AAClD,cAAM,OAAO,MAAM;AAEnB,YAAI,cAAc,MAAM;AACtB,uBAAa,KAAK,IAAI;AAAA,QACxB,OAAO;AACL,uBAAa,OAAO,IAAI;AAAA,QAC1B;AAEA,YAAI;AAAM;AAAA,MACZ;AAAA,IACF,OAAO;AACL,YAAM,OAAO,MAAM;AAEnB,UAAI,YAAY;AACd,qBAAa,KAAK,IAAI;AAAA,MACxB,OAAO;AACL,qBAAa,OAAO,IAAI;AAAA,MAC1B;AAAA,IACF;AAGA,mBAAe,QAAQ,MAAS;AAAA,EAClC;AAEA,QAAM,EAAE,MAAM,IAAI,eAAe,EAAE,WAAW,CAAC;AAE/C,QAAM,kBAAkB,kBAAkB;AAAA,IACxC,QAAQ,EAAE,QAAQ,QAAQ,SAAS;AAAA,IACnC,OAAO;AAAA;AAAA,EACT,CAAC;AACD,QAAM,SAAS,MAAM;AAAA,IAAM,YAAS;AA1QtC,UAAAC;AA2QI,mBAAM,SAAS;AAAA,QACb,MAAM;AAAA,UACJ,MAAM;AAAA,UACN,GAAG,0BAA0B;AAAA,YAC3B;AAAA,YACA;AAAA,YACA,aAAa;AAAA,UACf,CAAC;AAAA,QACH;AAAA,QACA,GAAG,oBAAoB,QAAQ;AAAA,QAC/B,aAAa,gBAAgB;AAAA,QAC7B,QAAQ,MAAM,6BAA6B;AAAA,UACzC,QAAQ;AAAA,UACR,wBAAwB,MAAM;AAAA,UAC9B,mBAAkBA,MAAA,MAAM,gBAAN,gBAAAA,IAAmB,KAAK;AAAA;AAAA,QAC5C,CAAC;AAAA,QACD,kBAAkB;AAAA,QAClB;AAAA,QACA;AAAA,MACF,CAAC;AAAA;AAAA,EACH;AAGA,QAAM,CAAC,QAAQ,YAAY,IAAI,OAAO,OAAO,IAAI;AACjD,GAAC,YAAY;AACX,QAAI;AACF,UAAI,UAAU;AACd,UAAI,cAAc;AAElB,YAAM,SAAS,aAAa,UAAU;AACtC,aAAO,MAAM;AACX,cAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,YAAI;AAAM;AAEV,gBAAQ,MAAM,MAAM;AAAA,UAClB,KAAK,cAAc;AACjB,uBAAW,MAAM;AACjB,mBAAO;AAAA,cACL,UAAU;AAAA,cACV,MAAM,CAAC,EAAE,SAAS,MAAM,OAAO,OAAO,MAAM,UAAU,CAAC;AAAA,cACvD,cAAc;AAAA,YAChB,CAAC;AACD;AAAA,UACF;AAAA,UAEA,KAAK,mBAAmB;AACtB,0BAAc;AACd;AAAA,UACF;AAAA,UAEA,KAAK,aAAa;AAChB,kBAAM,WAAW,MAAM;AAEvB,gBAAI,CAAC,OAAO;AACV,oBAAM,IAAI,gBAAgB,EAAE,SAAS,CAAC;AAAA,YACxC;AAEA,kBAAM,OAAO,MAAM,QAAQ;AAC3B,gBAAI,CAAC,MAAM;AACT,oBAAM,IAAI,gBAAgB;AAAA,gBACxB;AAAA,gBACA,gBAAgB,OAAO,KAAK,KAAK;AAAA,cACnC,CAAC;AAAA,YACH;AAEA,0BAAc;AACd,kBAAM,cAAc,cAAc;AAAA,cAChC,MAAM,MAAM;AAAA,cACZ,QAAQ,KAAK;AAAA,YACf,CAAC;AAED,gBAAI,YAAY,YAAY,OAAO;AACjC,oBAAM,IAAI,0BAA0B;AAAA,gBAClC;AAAA,gBACA,UAAU,MAAM;AAAA,gBAChB,OAAO,YAAY;AAAA,cACrB,CAAC;AAAA,YACH;AAEA,mBAAO;AAAA,cACL,UAAU,KAAK;AAAA,cACf,MAAM;AAAA,gBACJ,YAAY;AAAA,gBACZ;AAAA,kBACE;AAAA,kBACA,YAAY,MAAM;AAAA,gBACpB;AAAA,cACF;AAAA,cACA,cAAc;AAAA,cACd,YAAY;AAAA,YACd,CAAC;AAED;AAAA,UACF;AAAA,UAEA,KAAK,SAAS;AACZ,kBAAM,MAAM;AAAA,UACd;AAAA,UAEA,KAAK,UAAU;AACb,0BAAc;AAAA,cACZ,cAAc,MAAM;AAAA,cACpB,OAAO,4BAA4B,MAAM,KAAK;AAAA,cAC9C,UAAU,OAAO;AAAA,cACjB,aAAa,OAAO;AAAA,YACtB;AACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,UAAI,CAAC,aAAa;AAChB,eAAO;AAAA,UACL,UAAU;AAAA,UACV,MAAM,CAAC,EAAE,SAAS,MAAM,KAAK,CAAC;AAAA,UAC9B,cAAc;AAAA,UACd,YAAY;AAAA,QACd,CAAC;AAAA,MACH;AAEA,YAAM;AAEN,UAAI,eAAe,UAAU;AAC3B,cAAM,SAAS;AAAA,UACb,GAAG;AAAA,UACH,OAAO,GAAG;AAAA,QACZ,CAAC;AAAA,MACH;AAAA,IACF,SAAS,OAAO;AAGd,SAAG,MAAM,KAAK;AAAA,IAChB;AAAA,EACF,GAAG;AAEH,SAAO;AAAA,IACL,GAAG;AAAA,IACH;AAAA,IACA,OAAO,GAAG;AAAA,EACZ;AACF;;;AiCvZO,IAAM,wBAAwB,OAAO,IAAI,qBAAqB;;;ACQrE,IAAM,iCAAiC,OAAO,uBAAuB;AAMrE,SAAS,sBACP,cACA;AACA,QAAM,mBACJ,wBAAwB,kBACvB,OAAO,iBAAiB,YACvB,iBAAiB,QACjB,eAAe,gBACf,OAAO,aAAa,cAAc,cAClC,YAAY,gBACZ,OAAO,aAAa,WAAW;AAEnC,MAAI,CAAC,kBAAkB;AACrB,WAAO,0BAAgC,YAAY;AAAA,EACrD;AAEA,QAAM,kBAAkB,0BAAgC;AAMxD,kBAAgB,8BAA8B,IAAI;AAElD,GAAC,YAAY;AACX,QAAI;AAEF,YAAM,SAAS,aAAa,UAAU;AAEtC,aAAO,MAAM;AACX,cAAM,EAAE,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK;AAC1C,YAAI,MAAM;AACR;AAAA,QACF;AAGA,wBAAgB,8BAA8B,IAAI;AAClD,YAAI,OAAO,UAAU,UAAU;AAC7B,0BAAgB,OAAO,KAAK;AAAA,QAC9B,OAAO;AACL,0BAAgB,OAAO,KAAK;AAAA,QAC9B;AAEA,wBAAgB,8BAA8B,IAAI;AAAA,MACpD;AAEA,sBAAgB,8BAA8B,IAAI;AAClD,sBAAgB,KAAK;AAAA,IACvB,SAAS,GAAG;AACV,sBAAgB,8BAA8B,IAAI;AAClD,sBAAgB,MAAM,CAAC;AAAA,IACzB;AAAA,EACF,GAAG;AAEH,SAAO;AACT;AAuDA,SAAS,0BAA4C,cAAkB;AACrE,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAI,aAAa,wBAA+C;AAEhE,MAAI,eAAe;AACnB,MAAI;AACJ,MAAI,iBACF,WAAW;AACb,MAAI;AAEJ,WAAS,aAAa,QAAgB;AACpC,QAAI,QAAQ;AACV,YAAM,IAAI,MAAM,SAAS,mCAAmC;AAAA,IAC9D;AACA,QAAI,QAAQ;AACV,YAAM,IAAI;AAAA,QACR,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAEA,MAAI;AACJ,WAAS,qBAAqB;AAC5B,QAAI,QAAQ,IAAI,aAAa,eAAe;AAC1C,UAAI,gBAAgB;AAClB,qBAAa,cAAc;AAAA,MAC7B;AACA,uBAAiB,WAAW,MAAM;AAChC,gBAAQ;AAAA,UACN;AAAA,QACF;AAAA,MACF,GAAG,8BAA8B;AAAA,IACnC;AAAA,EACF;AACA,qBAAmB;AAEnB,WAAS,cAAc,cAA+C;AAEpE,QAAI;AAEJ,QAAI,iBAAiB,QAAW;AAC9B,aAAO,EAAE,OAAO,aAAa;AAAA,IAC/B,OAAO;AACL,UAAI,qBAAqB,CAAC,cAAc;AACtC,eAAO,EAAE,MAAM,kBAAkB;AAAA,MACnC,OAAO;AACL,eAAO,EAAE,MAAM,aAAa;AAAA,MAC9B;AAAA,IACF;AAEA,QAAI,gBAAgB;AAClB,WAAK,OAAO;AAAA,IACd;AAEA,QAAI,cAAc;AAChB,WAAK,OAAO;AAAA,IACd;AAEA,WAAO;AAAA,EACT;AAGA,WAAS,kBAAkB,OAAU;AAEnC,wBAAoB;AACpB,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,OAAO,iBAAiB,UAAU;AACpC,YAAI,MAAM,WAAW,YAAY,GAAG;AAClC,8BAAoB,CAAC,GAAG,MAAM,MAAM,aAAa,MAAM,CAAC;AAAA,QAC1D;AAAA,MACF;AAAA,IACF;AAEA,mBAAe;AAAA,EACjB;AAEA,QAAM,aAA2C;AAAA,IAC/C,KAAK,8BAA8B,EAAE,OAAgB;AACnD,eAAS;AAAA,IACX;AAAA,IACA,IAAI,QAAQ;AACV,aAAO,cAAc,IAAI;AAAA,IAC3B;AAAA,IACA,OAAO,OAAU;AACf,mBAAa,WAAW;AAExB,YAAM,kBAAkB,WAAW;AACnC,mBAAa,wBAAwB;AAErC,wBAAkB,KAAK;AACvB,uBAAiB,WAAW;AAC5B,sBAAgB,cAAc,CAAC;AAE/B,yBAAmB;AAEnB,aAAO;AAAA,IACT;AAAA,IACA,OAAO,OAAU;AACf,mBAAa,WAAW;AAExB,UACE,OAAO,iBAAiB,YACxB,OAAO,iBAAiB,aACxB;AACA,cAAM,IAAI;AAAA,UACR,2DAA2D,OAAO,YAAY;AAAA,QAChF;AAAA,MACF;AACA,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,IAAI;AAAA,UACR,mDAAmD,OAAO,KAAK;AAAA,QACjE;AAAA,MACF;AAEA,YAAM,kBAAkB,WAAW;AACnC,mBAAa,wBAAwB;AAErC,UAAI,OAAO,iBAAiB,UAAU;AACpC,4BAAoB,CAAC,GAAG,KAAK;AAC7B,QAAC,eAA0B,eAAe;AAAA,MAC5C,OAAO;AACL,4BAAoB;AACpB,uBAAe;AAAA,MACjB;AAEA,uBAAiB,WAAW;AAC5B,sBAAgB,cAAc,CAAC;AAE/B,yBAAmB;AAEnB,aAAO;AAAA,IACT;AAAA,IACA,MAAM,OAAY;AAChB,mBAAa,UAAU;AAEvB,UAAI,gBAAgB;AAClB,qBAAa,cAAc;AAAA,MAC7B;AACA,eAAS;AACT,qBAAe;AACf,uBAAiB;AAEjB,iBAAW,QAAQ,EAAE,MAAM,CAAC;AAE5B,aAAO;AAAA,IACT;AAAA,IACA,QAAQ,MAAgB;AACtB,mBAAa,SAAS;AAEtB,UAAI,gBAAgB;AAClB,qBAAa,cAAc;AAAA,MAC7B;AACA,eAAS;AACT,uBAAiB;AAEjB,UAAI,KAAK,QAAQ;AACf,0BAAkB,KAAK,CAAC,CAAC;AACzB,mBAAW,QAAQ,cAAc,CAAC;AAClC,eAAO;AAAA,MACT;AAEA,iBAAW,QAAQ,CAAC,CAAC;AAErB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;", "names": ["_a", "name", "_a", "_a", "convertBase64ToUint8Array", "AISDKError", "name", "marker", "symbol", "_a", "_a", "convertBase64ToUint8Array", "AISDKError", "name", "marker", "symbol", "_a", "_a", "AISDKError", "name", "marker", "symbol", "_a", "AISDKError", "name", "marker", "symbol", "_a", "name", "z", "_a", "AISDKError", "name", "marker", "symbol", "_a", "_a", "processBlock", "content", "i", "z", "z", "z", "z", "z", "z", "z", "z", "z", "AISDKError", "getErrorMessage", "name", "marker", "symbol", "_a", "AISDKError", "name", "marker", "symbol", "_a", "jsx", "name", "_a"]}