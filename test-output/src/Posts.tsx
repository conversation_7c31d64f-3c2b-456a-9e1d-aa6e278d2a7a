import React from 'react';
import './css/styles.css';
import { formatDate } from './utils'; // Assume formatDate is a utility function for date formatting

type Post = {
  title: string;
  postedDate: Date;
  postedText: string;
  postId: string;
};

type PostsProps = {
  posts: Post[];
};

const Posts: React.FC<PostsProps> = ({ posts }) => {
  return (
    <div className="max_width_400">
      {posts.map((post) => (
        <div key={post.postId}>
          <h3>{post.title}</h3>
          <p>{formatDate(post.postedDate)}</p>
          <p>{post.postedText}</p>
          <a href={`/posts/${post.postId}`}>Continue</a>
          <br />
        </div>
      ))}
      <form action="/posts" method="get">
        <input type="submit" name="create" value="Create new post" />
      </form>
    </div>
  );
};

export default Posts;