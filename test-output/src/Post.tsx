import React from 'react';
import './css/styles.css';
import { FormatDate } from './utils'; // Assume a utility function for date formatting

type PostProps = {
  post: {
    title: string;
    postedDate: Date;
    postedText: string;
    postId: string;
  };
};

const Post: React.FC<PostProps> = ({ post }) => {
  return (
    <div>
      <h2>{post.title}</h2>
      <h4><FormatDate date={post.postedDate} /></h4>
      <p className="format_post">{post.postedText}</p>
      <div className="max_width_100">
        <form className="buttons_center" action={`/posts/${post.postId}/delete`} method="post">
          <input type="submit" name="delete" value="Delete" />
        </form>
        <form className="buttons_center" action={`/posts/${post.postId}/edit`} method="get">
          <input type="submit" name="edit" value="Edit" />
        </form>
      </div>
    </div>
  );
};

export default Post;