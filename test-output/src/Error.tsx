import React from 'react';
import './css/styles.css';

interface ErrorProps {
  message?: string;
}

const Error: React.FC<ErrorProps> = ({ message }) => {
  return (
    <div>
      <h1>Page Not Found</h1>
      <h3>
        {message && <span>{message}</span>}
        {!message && <span>Internal error occurred. Please contact administrator.</span>}
      </h3>
      <a href="#" onClick={() => window.history.back()}>Back to Previous Page</a>
    </div>
  );
};

export default Error;