import React, { useState } from 'react';
import './css/styles.css';

interface CreatePostProps {
  posts?: any[];
  onAddPost?: (title: string, context: string) => void;
}

const CreatePost: React.FC<CreatePostProps> = ({ onAddPost }) => {
  const [title, setTitle] = useState('');
  const [context, setContext] = useState('');

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    if (onAddPost) {
      onAddPost(title, context);
    }
    setTitle('');
    setContext('');
  };

  return (
    <div>
      <h2>Create new post</h2>
      <form onSubmit={handleSubmit}>
        <h4>Title:</h4>
        <input className="text_title" type="text" name="title" value={title} onChange={(e) => setTitle(e.target.value)} required />
        <h4>Context:</h4>
        <textarea className="text_title" name="context" value={context} onChange={(e) => setContext(e.target.value)} required />
        <br />
        <br />
        <input type="submit" name="add" value="Add post" />
      </form>
    </div>
  );
};

export default CreatePost;