import React from 'react';
import './css/styles.css';
import { Post } from './types'; // Assuming there is a types file that defines the Post type

interface EditProps {
  post: Post;
}

const Edit: React.FC<EditProps> = ({ post }) => {
  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    // Handle form submission logic here
  };

  return (
    <div>
      <h2>Edit post "{post.title}"</h2>
      <form action={`/posts/${post.postId}/edit`} method="post" onSubmit={handleSubmit}>
        <h4>Title:</h4>
        <input className="text_title" type="text" name="title" value={post.title} required />
        <h4>Context:</h4>
        <textarea className="text_title" name="context" required>
          {post.postedText}
        </textarea>
        <br />
        <br />
        <input className="value_center" type="submit" name="save" value="Save" />
      </form>
    </div>
  );
};

export default Edit;