{"dynamicRoutes": [{"path": "/posts/**", "method": "ALL", "type": "servlet", "source": "annotation", "confidence": 0.95, "metadata": {"className": "BlogController", "annotation": "@WebServlet", "filePath": "fixtures/source/src/main/java/com/shpota/blog/controller/BlogController.java", "originalPattern": "/posts/*"}}], "staticRoutes": [{"path": "/create", "method": "ALL", "type": "servlet", "source": "annotation", "confidence": 0.95, "metadata": {"className": "BlogController", "annotation": "@WebServlet", "filePath": "fixtures/source/src/main/java/com/shpota/blog/controller/BlogController.java", "originalPattern": "/create"}}, {"path": "/create.jsp", "method": "GET", "type": "page", "source": "filesystem", "confidence": 0.8999999999999999, "metadata": {"filePath": "fixtures/source/src/main/webapp/create.jsp", "fileSize": 529, "extension": ".jsp", "directory": ".", "mimeType": "text/html"}}, {"path": "/css/styles.css", "method": "GET", "type": "stylesheet", "source": "filesystem", "confidence": 0.9999999999999999, "metadata": {"filePath": "fixtures/source/src/main/webapp/css/styles.css", "fileSize": 495, "extension": ".css", "directory": "css", "mimeType": "text/css"}}, {"path": "/edit.jsp", "method": "GET", "type": "page", "source": "filesystem", "confidence": 0.8999999999999999, "metadata": {"filePath": "fixtures/source/src/main/webapp/edit.jsp", "fileSize": 742, "extension": ".jsp", "directory": ".", "mimeType": "text/html"}}, {"path": "/error", "method": "ALL", "type": "servlet", "source": "annotation", "confidence": 0.95, "metadata": {"className": "BlogController", "annotation": "@WebServlet", "filePath": "fixtures/source/src/main/java/com/shpota/blog/controller/BlogController.java", "originalPattern": "/error"}}, {"path": "/error.jsp", "method": "GET", "type": "page", "source": "filesystem", "confidence": 0.8999999999999999, "metadata": {"filePath": "fixtures/source/src/main/webapp/error.jsp", "fileSize": 543, "extension": ".jsp", "directory": ".", "mimeType": "text/html"}}, {"path": "/post.jsp", "method": "GET", "type": "page", "source": "filesystem", "confidence": 0.8999999999999999, "metadata": {"filePath": "fixtures/source/src/main/webapp/post.jsp", "fileSize": 810, "extension": ".jsp", "directory": ".", "mimeType": "text/html"}}, {"path": "/posts.jsp", "method": "GET", "type": "page", "source": "filesystem", "confidence": 0.8999999999999999, "metadata": {"filePath": "fixtures/source/src/main/webapp/posts.jsp", "fileSize": 774, "extension": ".jsp", "directory": ".", "mimeType": "text/html"}}], "nestedRoutes": [], "suggestions": ["Consider using React Router dynamic segments for parameterized routes"]}