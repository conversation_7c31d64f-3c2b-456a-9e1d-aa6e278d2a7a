const path = require('path');
const WebXmlParser = require('./parsers/WebXmlParser');
const JavaAnnotationScanner = require('./parsers/JavaAnnotationScanner');
const StaticResourceMapper = require('./parsers/StaticResourceMapper');
const RouteManifestGenerator = require('./RouteManifestGenerator');

/**
 * Main RouteDiscovery class that orchestrates the discovery process
 */
class RouteDiscovery {
  constructor(options = {}) {
    this.options = {
      includeStaticResources: true,
      includeWebXml: true,
      includeAnnotations: true,
      minConfidence: 0.0,
      deduplicateRoutes: true,
      ...options
    };

    this.webXmlParser = new WebXmlParser();
    this.annotationScanner = new JavaAnnotationScanner();
    this.staticMapper = new StaticResourceMapper();
    this.manifestGenerator = new RouteManifestGenerator();
  }

  /**
   * Discover all routes in a JSP/Java web application
   * @param {string} projectPath - Path to the web application project
   * @returns {Promise<RouteManifest>} Complete route manifest
   */
  async discoverRoutes(projectPath) {
    try {
      console.log(`Starting route discovery for project: ${projectPath}`);
      
      // Clear any previous results
      this.manifestGenerator.clear();

      // Discover routes from different sources
      await this.discoverFromWebXml(projectPath);
      await this.discoverFromAnnotations(projectPath);
      await this.discoverFromStaticResources(projectPath);

      // Post-process routes
      this.postProcessRoutes();

      // Generate final manifest
      const manifest = this.manifestGenerator.generateManifest(projectPath);
      
      console.log(`Route discovery completed. Found ${manifest.routes.length} routes.`);
      return manifest;
    } catch (error) {
      throw new Error(`Route discovery failed: ${error.message}`);
    }
  }

  /**
   * Discover routes from web.xml configuration
   * @param {string} projectPath - Project path
   */
  async discoverFromWebXml(projectPath) {
    if (!this.options.includeWebXml) {
      return;
    }

    try {
      const webXmlPath = path.join(projectPath, 'src/main/webapp/WEB-INF/web.xml');
      console.log(`Scanning web.xml: ${webXmlPath}`);
      
      const servletMappings = await this.webXmlParser.parseWebXml(webXmlPath);
      const routes = this.webXmlParser.convertToRoutes(servletMappings);
      
      this.manifestGenerator.addRoutes(routes, 'web.xml');
      console.log(`Found ${routes.length} routes from web.xml`);
    } catch (error) {
      console.warn(`Warning: Could not parse web.xml: ${error.message}`);
    }
  }

  /**
   * Discover routes from Java annotations
   * @param {string} projectPath - Project path
   */
  async discoverFromAnnotations(projectPath) {
    if (!this.options.includeAnnotations) {
      return;
    }

    try {
      const javaSourcePath = path.join(projectPath, 'src/main/java');
      console.log(`Scanning Java annotations in: ${javaSourcePath}`);
      
      const routes = await this.annotationScanner.scanJavaFiles(javaSourcePath);
      
      this.manifestGenerator.addRoutes(routes, 'annotations');
      console.log(`Found ${routes.length} routes from annotations`);
    } catch (error) {
      console.warn(`Warning: Could not scan Java annotations: ${error.message}`);
    }
  }

  /**
   * Discover routes from static resources
   * @param {string} projectPath - Project path
   */
  async discoverFromStaticResources(projectPath) {
    if (!this.options.includeStaticResources) {
      return;
    }

    try {
      const webappPath = path.join(projectPath, 'src/main/webapp');
      console.log(`Scanning static resources in: ${webappPath}`);
      
      const routes = await this.staticMapper.scanStaticResources(webappPath);
      
      this.manifestGenerator.addRoutes(routes, 'static');
      console.log(`Found ${routes.length} static resource routes`);
    } catch (error) {
      console.warn(`Warning: Could not scan static resources: ${error.message}`);
    }
  }

  /**
   * Post-process discovered routes
   */
  postProcessRoutes() {
    // Filter by minimum confidence
    if (this.options.minConfidence > 0) {
      const originalCount = this.manifestGenerator.routes.length;
      this.manifestGenerator.routes = this.manifestGenerator.routes.filter(
        route => route.confidence >= this.options.minConfidence
      );
      const filteredCount = originalCount - this.manifestGenerator.routes.length;
      if (filteredCount > 0) {
        console.log(`Filtered out ${filteredCount} routes below confidence threshold`);
      }
    }

    // Remove duplicates if requested
    if (this.options.deduplicateRoutes) {
      const originalCount = this.manifestGenerator.routes.length;
      this.manifestGenerator.deduplicateRoutes();
      const duplicatesRemoved = originalCount - this.manifestGenerator.routes.length;
      if (duplicatesRemoved > 0) {
        console.log(`Removed ${duplicatesRemoved} duplicate routes`);
      }
    }

    // Sort routes by path for consistent output
    this.manifestGenerator.sortRoutes('path', 'asc');
  }

  /**
   * Get routes with optional filtering
   * @param {Object} filters - Filter criteria
   * @returns {Route[]} Filtered routes
   */
  getRoutes(filters = {}) {
    return this.manifestGenerator.getRoutes(filters);
  }

  /**
   * Export route manifest to file
   * @param {string} outputPath - Output file path
   * @param {string} projectPath - Source project path
   * @returns {Promise<void>}
   */
  async exportManifest(outputPath, projectPath) {
    return this.manifestGenerator.exportToFile(outputPath, projectPath);
  }

  /**
   * Generate a human-readable summary
   * @returns {string} Summary text
   */
  generateSummary() {
    return this.manifestGenerator.generateSummary();
  }

  /**
   * Analyze route patterns and suggest React Router structure
   * @returns {Object} Analysis and suggestions
   */
  analyzeForReactRouter() {
    const routes = this.manifestGenerator.routes;
    const analysis = {
      dynamicRoutes: [],
      staticRoutes: [],
      nestedRoutes: [],
      suggestions: []
    };

    routes.forEach(route => {
      // Skip static resources for React Router analysis
      if (route.type === 'static' || route.type === 'image' || route.type === 'stylesheet') {
        analysis.staticRoutes.push(route);
        return;
      }

      // Detect dynamic routes (with parameters)
      if (route.path.includes('/:') || route.path.match(/\/\d+/) || route.path.includes('/*')) {
        analysis.dynamicRoutes.push(route);
      } else {
        analysis.staticRoutes.push(route);
      }

      // Detect potential nested routes
      const pathSegments = route.path.split('/').filter(segment => segment.length > 0);
      if (pathSegments.length > 2) {
        analysis.nestedRoutes.push(route);
      }
    });

    // Generate suggestions
    if (analysis.dynamicRoutes.length > 0) {
      analysis.suggestions.push('Consider using React Router dynamic segments for parameterized routes');
    }

    if (analysis.nestedRoutes.length > 0) {
      analysis.suggestions.push('Consider organizing nested routes with React Router nested routing');
    }

    if (analysis.staticRoutes.length > 10) {
      analysis.suggestions.push('Consider grouping related static routes under common layouts');
    }

    return analysis;
  }
}

module.exports = RouteDiscovery;
