require('dotenv').config();
const { generateText } = require('ai');
const { createOpenAI } = require('@ai-sdk/openai');

/**
 * LLM Provider Configuration
 * 
 * Supports multiple LLM providers with automatic fallback:
 * 1. DeepSeek (Prioritized)
 * 2. GLM (智谱AI)
 * 3. OpenAI
 */
class LLMProvider {
  constructor() {
    this.config = this.configureLLMProvider();
    if (!this.config) {
      throw new Error('No LLM provider configured. Please set up API keys in .env file.');
    }
    
    console.log(`🤖 Using ${this.config.providerName} as LLM provider`);
  }

  configureLLMProvider() {
    // DeepSeek Provider (Prioritized)
    if (process.env.DEEPSEEK_TOKEN) {
      const openai = createOpenAI({
        compatibility: "compatible",
        baseURL: process.env.DEEPSEEK_BASE_URL || "https://api.deepseek.com/v1",
        apiKey: process.env.DEEPSEEK_TOKEN,
      });

      return {
        fullModel: process.env.DEEPSEEK_MODEL || "deepseek-chat",
        quickModel: process.env.DEEPSEEK_MODEL || "deepseek-chat",
        openai,
        providerName: "DeepSeek"
      };
    }

    // GLM Provider (智谱AI)
    if (process.env.GLM_API_KEY || process.env.GLM_TOKEN) {
      const apiKey = process.env.GLM_API_KEY || process.env.GLM_TOKEN;
      const openai = createOpenAI({
        compatibility: "compatible",
        baseURL: process.env.LLM_BASE_URL || "https://open.bigmodel.cn/api/paas/v4",
        apiKey: apiKey,
      });

      return {
        fullModel: process.env.LLM_MODEL || "glm-4-air",
        quickModel: process.env.LLM_MODEL || "glm-4-air",
        openai,
        providerName: "GLM"
      };
    }

    // OpenAI Provider
    if (process.env.OPENAI_API_KEY) {
      const openai = createOpenAI({
        compatibility: "strict",
        apiKey: process.env.OPENAI_API_KEY,
        baseURL: process.env.OPENAI_BASE_URL,
      });

      return {
        fullModel: process.env.OPENAI_MODEL || "gpt-4o-mini",
        quickModel: process.env.OPENAI_MODEL || "gpt-4o-mini",
        openai,
        providerName: "OpenAI"
      };
    }

    return null;
  }

  /**
   * Generate text using the configured LLM
   */
  async generateText(prompt, options = {}) {
    const model = options.useQuickModel ? this.config.quickModel : this.config.fullModel;
    
    try {
      const result = await generateText({
        model: this.config.openai(model),
        prompt,
        temperature: options.temperature || 0.1,
        maxTokens: options.maxTokens || 4000,
        ...options
      });
      
      return result.text;
    } catch (error) {
      console.error(`❌ LLM generation failed (${this.config.providerName}):`, error.message);
      throw error;
    }
  }

  /**
   * Generate JSP to React conversion
   */
  async convertJSPToReact(jspContent, componentName, options = {}) {
    const prompt = this.buildConversionPrompt(jspContent, componentName, options);
    
    try {
      console.log(`🤖 Converting ${componentName} using ${this.config.providerName}...`);
      
      const result = await this.generateText(prompt, {
        temperature: 0.1,
        maxTokens: 6000,
        ...options
      });
      
      return this.parseConversionResult(result);
    } catch (error) {
      console.error(`❌ AI conversion failed for ${componentName}:`, error.message);
      throw error;
    }
  }

  /**
   * Build conversion prompt for JSP to React
   */
  buildConversionPrompt(jspContent, componentName, options = {}) {
    const useTypeScript = options.useTypeScript !== false;
    const fileExtension = useTypeScript ? 'tsx' : 'jsx';
    
    return `You are an expert frontend developer specializing in JSP to React migration. 

Convert the following JSP file to a modern React component:

**Requirements:**
1. Convert to ${useTypeScript ? 'TypeScript' : 'JavaScript'} React component (${fileExtension})
2. Transform JSTL tags to equivalent JSX:
   - <c:forEach> → Array.map()
   - <c:if> → Conditional rendering with &&
   - <c:choose/when/otherwise> → Ternary operators or if-else
3. Convert EL expressions \${...} to JSX expressions {...}
4. Transform HTML attributes: class → className, for → htmlFor
5. Handle form elements properly (self-closing tags)
6. Convert CSS links to import statements
7. Make the component functional and modern
8. Add proper TypeScript types if applicable
9. Handle data props appropriately (assume data comes from props)
10. Add error boundaries and loading states where appropriate
11. Convert CSS imports to relative paths (e.g., '/css/styles.css' → './css/styles.css')
12. Ensure all components are properly typed and functional

**Component Name:** ${componentName}

**JSP Content:**
\`\`\`jsp
${jspContent}
\`\`\`

**Output Format:**
Please provide ONLY the React component code without any explanations or markdown formatting. The output should be ready to save as a ${fileExtension} file.

**Important Notes:**
- Assume data like \${posts}, \${post}, etc. will be passed as props
- Convert Java date formatting to JavaScript date handling
- Use modern React patterns (hooks, functional components)
- Ensure the component is self-contained and importable
- Handle CSS imports properly (convert /css/styles.css to './css/styles.css')`;
  }

  /**
   * Parse and clean the conversion result
   */
  parseConversionResult(result) {
    // Remove markdown code blocks if present
    let cleanResult = result.replace(/```[a-zA-Z]*\n?/g, '').trim();
    
    // Remove any leading/trailing whitespace
    cleanResult = cleanResult.trim();
    
    // Ensure the result starts with import statements or component definition
    if (!cleanResult.startsWith('import') && !cleanResult.startsWith('export') && !cleanResult.startsWith('function')) {
      // If it doesn't start properly, try to find the actual component code
      const lines = cleanResult.split('\n');
      let startIndex = 0;
      
      for (let i = 0; i < lines.length; i++) {
        if (lines[i].startsWith('import') || lines[i].startsWith('export') || lines[i].startsWith('function')) {
          startIndex = i;
          break;
        }
      }
      
      cleanResult = lines.slice(startIndex).join('\n');
    }
    
    return cleanResult;
  }

  /**
   * Generate improved component with AI suggestions
   */
  async improveComponent(componentCode, componentName, issues = []) {
    const prompt = `You are a React expert. Please improve the following React component:

**Component Name:** ${componentName}

**Current Issues:** ${issues.length > 0 ? issues.join(', ') : 'General improvements needed'}

**Component Code:**
\`\`\`jsx
${componentCode}
\`\`\`

**Improvement Requirements:**
1. Fix any syntax errors
2. Improve TypeScript types if applicable
3. Add proper error handling
4. Optimize performance
5. Follow React best practices
6. Ensure accessibility
7. Add proper prop validation
8. Fix any logical issues

Please provide ONLY the improved component code without explanations.`;

    try {
      const result = await this.generateText(prompt, {
        temperature: 0.1,
        maxTokens: 6000
      });
      
      return this.parseConversionResult(result);
    } catch (error) {
      console.error(`❌ Component improvement failed for ${componentName}:`, error.message);
      return componentCode; // Return original if improvement fails
    }
  }

  /**
   * Check if LLM provider is available
   */
  isAvailable() {
    return this.config !== null;
  }

  /**
   * Get provider information
   */
  getProviderInfo() {
    return {
      name: this.config?.providerName || 'None',
      model: this.config?.fullModel || 'None',
      available: this.isAvailable()
    };
  }
}

module.exports = LLMProvider;
