#!/usr/bin/env node

const path = require('path');
const fs = require('fs').promises;

// Import core modules
const RouteDiscovery = require('./analysis/RouteDiscovery');
const JSPToReactConverter = require('./conversion/JSPToReactConverter');
const AIJSPToReactConverter = require('./conversion/AIJSPToReactConverter');

/**
 * JSP to React Migration CLI Tool
 * 
 * This CLI tool implements the comprehensive JSP to React migration strategy
 * outlined in the README.md, supporting:
 * - Deep application analysis
 * - Intelligent code conversion
 * - Hybrid migration mode
 * - Automated testing and validation
 */
class JSP2ReactCLI {
  constructor() {
    this.commands = {
      analyze: this.analyzeCommand.bind(this),
      convert: this.convertCommand.bind(this),
      hybrid: this.hybridCommand.bind(this),
      test: this.testCommand.bind(this),
      help: this.helpCommand.bind(this)
    };
  }

  /**
   * Parse command line arguments
   */
  parseArgs(args) {
    const command = args[2] || 'help';
    const options = {};
    const positional = [];

    for (let i = 3; i < args.length; i++) {
      const arg = args[i];
      if (arg.startsWith('--')) {
        const [key, value] = arg.slice(2).split('=');
        options[key] = value || true;
      } else if (arg.startsWith('-')) {
        options[arg.slice(1)] = true;
      } else {
        positional.push(arg);
      }
    }

    return { command, options, positional };
  }

  /**
   * Phase 1: Deep Application Analysis
   * Implements comprehensive JSP application analysis as outlined in README
   */
  async analyzeCommand(options, positional) {
    const sourcePath = positional[0] || './fixtures/source';
    const outputPath = options.output || './analysis-results';

    console.log('🔍 JSP to React Migration - Deep Analysis Phase');
    console.log('================================================\n');

    try {
      // Ensure output directory exists
      await fs.mkdir(outputPath, { recursive: true });

      // 1. Advanced Route Discovery
      console.log('📍 Phase 1.1: Advanced Route Discovery');
      const discovery = new RouteDiscovery({
        includeStaticResources: true,
        includeWebXml: true,
        includeAnnotations: true,
        minConfidence: 0.0,
        deduplicateRoutes: true
      });

      const manifest = await discovery.discoverRoutes(sourcePath);
      const manifestPath = path.join(outputPath, 'route-manifest.json');
      await discovery.exportManifest(manifestPath, sourcePath);
      
      console.log(`✅ Route manifest exported to: ${manifestPath}`);
      console.log(discovery.generateSummary());

      // 2. React Router Analysis
      console.log('\n📊 Phase 1.2: React Router Analysis');
      const analysis = discovery.analyzeForReactRouter();
      const analysisPath = path.join(outputPath, 'react-router-analysis.json');
      await fs.writeFile(analysisPath, JSON.stringify(analysis, null, 2));
      
      console.log(`✅ React Router analysis saved to: ${analysisPath}`);
      console.log(`Dynamic routes: ${analysis.dynamicRoutes.length}`);
      console.log(`Static routes: ${analysis.staticRoutes.length}`);
      console.log(`Nested routes: ${analysis.nestedRoutes.length}`);

      if (analysis.suggestions.length > 0) {
        console.log('\n💡 Suggestions:');
        analysis.suggestions.forEach(suggestion => {
          console.log(`  • ${suggestion}`);
        });
      }

      // 3. UI Pattern & Data Dependency Identification (Placeholder)
      console.log('\n🎨 Phase 1.3: UI Pattern & Data Dependency Analysis');
      console.log('⚠️  This phase requires dynamic analysis with running JSP application');
      console.log('   Please ensure your JSP application is running and accessible');
      
      // TODO: Implement dynamic analysis with Puppeteer
      // This would include:
      // - Capturing rendered HTML from running JSP pages
      // - Intercepting AJAX requests
      // - Mapping UI components to data dependencies

      // 4. Initial API Contract Generation (Placeholder)
      console.log('\n📋 Phase 1.4: API Contract Generation');
      console.log('⚠️  API contract generation will be implemented in future iterations');
      
      // TODO: Generate OpenAPI 3.0 specifications based on discovered routes and data flows

      console.log('\n🎉 Deep Analysis Phase Complete!');
      console.log(`📁 Results saved in: ${outputPath}`);

    } catch (error) {
      console.error('❌ Analysis failed:', error.message);
      if (options.verbose) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  }

  /**
   * Phase 3: Automated Code Conversion
   * Implements JSP to React conversion pipeline
   */
  async convertCommand(options, positional) {
    const sourcePath = positional[0] || './fixtures/source';
    const targetPath = positional[1] || './output/react-components';

    console.log('🔄 JSP to React Migration - Code Conversion Phase');
    console.log('=================================================\n');

    try {
      // 1. Load analysis results if available
      const analysisPath = options.analysis || './analysis-results';
      let routeManifest = null;

      try {
        const manifestPath = path.join(analysisPath, 'route-manifest.json');
        const manifestContent = await fs.readFile(manifestPath, 'utf-8');
        routeManifest = JSON.parse(manifestContent);
        console.log(`📋 Loaded route manifest from: ${manifestPath}`);
      } catch (error) {
        console.log('⚠️  No route manifest found, proceeding without route analysis');
      }

      // 2. Initialize JSP to React converter (AI-powered or rule-based)
      const useAI = !options['no-ai'] && options.ai !== false; // Default to AI unless explicitly disabled
      const ConverterClass = useAI ? AIJSPToReactConverter : JSPToReactConverter;

      const converter = new ConverterClass({
        outputFormat: options.typescript ? 'tsx' : 'jsx',
        useTypeScript: options.typescript || true,
        preserveComments: options.comments !== false,
        useAI: useAI,
        fallbackToRules: options.fallback !== false
      });

      // 3. Find JSP source directory
      const jspSourceDir = path.join(sourcePath, 'src/main/webapp');

      console.log(`📁 Source JSP directory: ${jspSourceDir}`);
      console.log(`📁 Target React directory: ${targetPath}`);

      // 4. Convert JSP files to React components
      console.log('\n🔄 Phase 3.1: JSP to React Component Conversion');
      const conversionResults = await converter.convertDirectory(jspSourceDir, targetPath);

      // 5. Generate React Router configuration
      console.log('\n🛣️  Phase 3.2: React Router Configuration Generation');
      await this.generateReactRouterConfig(routeManifest, targetPath, conversionResults);

      // 6. Copy CSS and static assets
      console.log('\n📁 Phase 3.3: Static Assets Copy');
      await this.copyStaticAssets(sourcePath, targetPath);

      // 7. Generate package.json and basic setup files
      console.log('\n📦 Phase 3.4: Project Setup Generation');
      await this.generateProjectSetup(targetPath, options);

      console.log('\n🎉 Code Conversion Phase Complete!');
      console.log(`📁 React components generated in: ${targetPath}`);

    } catch (error) {
      console.error('❌ Conversion failed:', error.message);
      if (options.verbose) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  }

  /**
   * Phase 4: Hybrid Mode & Gradual Migration
   * Implements "Strangler Fig" pattern for smooth transition
   */
  async hybridCommand(options, positional) {
    console.log('🔀 JSP to React Migration - Hybrid Mode');
    console.log('=======================================\n');

    console.log('⚠️  Hybrid mode is not yet implemented');
    console.log('   This phase will include:');
    console.log('   • React component embedding in JSP pages');
    console.log('   • JSP-React communication bridge');
    console.log('   • Backend API refactoring support');
    console.log('   • Progressive migration tracking');
  }

  /**
   * Phase 5 & 6: Testing and Validation
   * Implements comprehensive testing strategy
   */
  async testCommand(options, positional) {
    console.log('🧪 JSP to React Migration - Testing & Validation');
    console.log('================================================\n');

    console.log('⚠️  Testing framework is not yet implemented');
    console.log('   This phase will include:');
    console.log('   • Unit test generation for React components');
    console.log('   • E2E testing with Playwright/Cypress');
    console.log('   • Visual regression testing');
    console.log('   • API contract testing');
  }

  /**
   * Generate React Router configuration from route manifest
   */
  async generateReactRouterConfig(routeManifest, outputDir, conversionResults) {
    const componentMap = new Map();

    // Map JSP files to generated components
    conversionResults.forEach(result => {
      if (result.success) {
        const jspName = path.basename(result.inputPath, '.jsp');
        componentMap.set(jspName, result.componentName);
      }
    });

    // If no route manifest, generate routes from conversion results
    let routes = [];
    if (routeManifest && routeManifest.routes) {
      routes = routeManifest.routes.filter(route => route.type === 'page');
    } else {
      // Generate basic routes from converted components
      routes = conversionResults.filter(r => r.success).map(result => {
        const jspName = path.basename(result.inputPath, '.jsp');
        return {
          path: `/${jspName}.jsp`,
          type: 'page'
        };
      });
    }

    const routerConfig = this.generateRouterConfigCode(routes, componentMap);
    const routerPath = path.join(outputDir, 'AppRouter.tsx');

    await fs.writeFile(routerPath, routerConfig);
    console.log(`✅ React Router configuration generated: ${routerPath}`);
  }

  /**
   * Generate React Router configuration code
   */
  generateRouterConfigCode(routes, componentMap) {
    const imports = [];
    const routeElements = [];

    routes.forEach(route => {
      const jspName = path.basename(route.path, '.jsp');
      const componentName = componentMap.get(jspName);

      if (componentName) {
        imports.push(`import ${componentName} from './${componentName}';`);

        // Convert JSP path to React Router path
        let routePath = route.path;
        if (routePath.endsWith('.jsp')) {
          routePath = routePath.slice(0, -4);
        }
        if (routePath === '/index' || routePath === 'index') {
          routePath = '/';
        }

        routeElements.push(`        <Route path="${routePath}" element={<${componentName} />} />`);
      }
    });

    return `import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
${imports.join('\n')}

export default function AppRouter() {
  return (
    <Router>
      <Routes>
${routeElements.join('\n')}
      </Routes>
    </Router>
  );
}`;
  }

  /**
   * Copy static assets (CSS, images, etc.)
   */
  async copyStaticAssets(sourcePath, outputDir) {
    try {
      const webappDir = path.join(sourcePath, 'src/main/webapp');
      const publicDir = path.join(outputDir, 'public');
      const srcDir = path.join(outputDir, 'src');

      // Create directories
      await fs.mkdir(publicDir, { recursive: true });
      await fs.mkdir(path.join(srcDir, 'css'), { recursive: true });

      // Copy CSS files
      const cssDir = path.join(webappDir, 'css');
      try {
        const cssFiles = await fs.readdir(cssDir);
        for (const cssFile of cssFiles) {
          if (cssFile.endsWith('.css')) {
            const sourceCss = path.join(cssDir, cssFile);
            const targetCss = path.join(srcDir, 'css', cssFile);
            await fs.copyFile(sourceCss, targetCss);
            console.log(`✅ Copied CSS: ${cssFile}`);
          }
        }
      } catch (error) {
        console.log('⚠️  No CSS directory found, skipping CSS copy');
      }

      // Copy other static assets (images, etc.)
      try {
        const entries = await fs.readdir(webappDir, { withFileTypes: true });
        for (const entry of entries) {
          if (entry.isFile() && !entry.name.endsWith('.jsp')) {
            const sourceFile = path.join(webappDir, entry.name);
            const targetFile = path.join(publicDir, entry.name);
            await fs.copyFile(sourceFile, targetFile);
            console.log(`✅ Copied asset: ${entry.name}`);
          }
        }
      } catch (error) {
        console.log('⚠️  Error copying static assets:', error.message);
      }

    } catch (error) {
      console.log('⚠️  Static assets copy failed:', error.message);
    }
  }

  /**
   * Generate basic project setup files
   */
  async generateProjectSetup(outputDir, options) {
    // Generate package.json
    const packageJson = {
      name: "converted-react-app",
      version: "1.0.0",
      description: "React app converted from JSP",
      main: "index.js",
      scripts: {
        start: "react-scripts start",
        build: "react-scripts build",
        test: "react-scripts test",
        eject: "react-scripts eject"
      },
      dependencies: {
        react: "^18.2.0",
        "react-dom": "^18.2.0",
        "react-router-dom": "^6.8.0",
        "react-scripts": "5.0.1"
      },
      devDependencies: {}
    };

    if (options.typescript) {
      packageJson.dependencies["@types/react"] = "^18.0.0";
      packageJson.dependencies["@types/react-dom"] = "^18.0.0";
      packageJson.dependencies["typescript"] = "^4.9.0";
    }

    const packagePath = path.join(outputDir, 'package.json');
    await fs.writeFile(packagePath, JSON.stringify(packageJson, null, 2));
    console.log(`✅ Package.json generated: ${packagePath}`);

    // Generate basic index file
    // Generate src directory structure
    const srcDir = path.join(outputDir, 'src');
    await fs.mkdir(srcDir, { recursive: true });

    const indexContent = options.typescript ?
      this.generateIndexTSX() : this.generateIndexJSX();

    const indexPath = path.join(srcDir, `index.${options.typescript ? 'tsx' : 'jsx'}`);
    await fs.writeFile(indexPath, indexContent);
    console.log(`✅ Index file generated: ${indexPath}`);

    // Generate public/index.html
    const publicDir = path.join(outputDir, 'public');
    await fs.mkdir(publicDir, { recursive: true });

    const htmlContent = this.generateIndexHTML();
    const htmlPath = path.join(publicDir, 'index.html');
    await fs.writeFile(htmlPath, htmlContent);
    console.log(`✅ HTML template generated: ${htmlPath}`);

    // Move components to src directory
    await this.moveComponentsToSrc(outputDir, srcDir);

    // Generate tsconfig.json if TypeScript
    if (options.typescript) {
      const tsconfigContent = this.generateTSConfig();
      const tsconfigPath = path.join(outputDir, 'tsconfig.json');
      await fs.writeFile(tsconfigPath, tsconfigContent);
      console.log(`✅ TypeScript config generated: ${tsconfigPath}`);
    }
  }

  /**
   * Move components to src directory
   */
  async moveComponentsToSrc(outputDir, srcDir) {
    try {
      const files = await fs.readdir(outputDir);
      for (const file of files) {
        if (file.endsWith('.tsx') || file.endsWith('.jsx')) {
          const sourcePath = path.join(outputDir, file);
          const targetPath = path.join(srcDir, file);
          await fs.rename(sourcePath, targetPath);
        }
      }
      console.log(`✅ Components moved to src directory`);
    } catch (error) {
      console.log('⚠️  Error moving components:', error.message);
    }
  }

  /**
   * Generate index.html template
   */
  generateIndexHTML() {
    return `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="React app converted from JSP" />
    <title>Converted React App</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>`;
  }

  /**
   * Generate tsconfig.json
   */
  generateTSConfig() {
    return JSON.stringify({
      "compilerOptions": {
        "target": "es5",
        "lib": [
          "dom",
          "dom.iterable",
          "es6"
        ],
        "allowJs": true,
        "skipLibCheck": true,
        "esModuleInterop": true,
        "allowSyntheticDefaultImports": true,
        "strict": true,
        "forceConsistentCasingInFileNames": true,
        "noFallthroughCasesInSwitch": true,
        "module": "esnext",
        "moduleResolution": "node",
        "resolveJsonModule": true,
        "isolatedModules": true,
        "noEmit": true,
        "jsx": "react-jsx"
      },
      "include": [
        "src"
      ]
    }, null, 2);
  }

  /**
   * Generate index.tsx content
   */
  generateIndexTSX() {
    return `import React from 'react';
import ReactDOM from 'react-dom/client';
import AppRouter from './AppRouter';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <AppRouter />
  </React.StrictMode>
);`;
  }

  /**
   * Generate index.jsx content
   */
  generateIndexJSX() {
    return `import React from 'react';
import ReactDOM from 'react-dom/client';
import AppRouter from './AppRouter';

const root = ReactDOM.createRoot(document.getElementById('root'));

root.render(
  <React.StrictMode>
    <AppRouter />
  </React.StrictMode>
);`;
  }

  /**
   * Display help information
   */
  helpCommand() {
    console.log('JSP to React Migration CLI Tool');
    console.log('==============================\n');
    
    console.log('USAGE:');
    console.log('  npm run dev <command> [options] [arguments]\n');
    
    console.log('COMMANDS:');
    console.log('  analyze [source]     Perform deep analysis of JSP application');
    console.log('  convert [src] [dst]  Convert JSP files to React components');
    console.log('  hybrid               Enable hybrid migration mode');
    console.log('  test                 Run migration tests and validation');
    console.log('  help                 Show this help message\n');
    
    console.log('OPTIONS:');
    console.log('  --output=<path>      Specify output directory (default: ./analysis-results)');
    console.log('  --analysis=<path>    Path to analysis results (default: ./analysis-results)');
    console.log('  --typescript         Generate TypeScript files (.tsx)');
    console.log('  --ai                 Enable AI-powered conversion (default: true)');
    console.log('  --no-ai              Disable AI and use rule-based conversion');
    console.log('  --fallback           Enable fallback to rules if AI fails (default: true)');
    console.log('  --verbose            Enable verbose logging');
    console.log('  --dry-run            Show what would be done without making changes\n');
    
    console.log('EXAMPLES:');
    console.log('  npm run dev analyze ./fixtures/source');
    console.log('  npm run dev convert ./fixtures/source ./output/react-components');
    console.log('  npm run dev convert ./fixtures/source ./output --typescript --ai');
    console.log('  npm run dev convert ./fixtures/source ./output --no-ai');
    console.log('  npm run dev analyze --output=./my-analysis --verbose\n');
    
    console.log('For more information, see README.md');
  }

  /**
   * Main CLI entry point
   */
  async run(args = process.argv) {
    const { command, options, positional } = this.parseArgs(args);

    if (!this.commands[command]) {
      console.error(`❌ Unknown command: ${command}`);
      console.error('Run "npm run dev help" for usage information.');
      process.exit(1);
    }

    try {
      await this.commands[command](options, positional);
    } catch (error) {
      console.error('❌ Command failed:', error.message);
      if (options.verbose) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  }
}

// Run CLI if this file is executed directly
if (require.main === module) {
  const cli = new JSP2ReactCLI();
  cli.run();
}

module.exports = JSP2ReactCLI;
