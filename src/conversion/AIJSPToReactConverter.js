const fs = require('fs').promises;
const path = require('path');
const LLMProvider = require('../common/LLMProvider');

/**
 * AI-Powered JSP to React Converter
 * 
 * Uses LLM to intelligently convert JSP files to React components
 * with better understanding of context and business logic.
 */
class AIJSPToReactConverter {
  constructor(options = {}) {
    this.options = {
      outputFormat: 'tsx', // tsx or jsx
      useTypeScript: true,
      preserveComments: true,
      generatePropTypes: false,
      useAI: true,
      fallbackToRules: true, // Fallback to rule-based conversion if AI fails
      ...options
    };

    // Initialize LLM provider if AI is enabled
    if (this.options.useAI) {
      try {
        this.llmProvider = new LLMProvider();
      } catch (error) {
        console.warn('⚠️  LLM provider not available, falling back to rule-based conversion');
        this.options.useAI = false;
      }
    }
  }

  /**
   * Convert a single JSP file to React component using AI
   */
  async convertFile(jspFilePath, outputDir) {
    try {
      console.log(`🔄 Converting: ${jspFilePath}`);
      
      // Read JSP file
      const jspContent = await fs.readFile(jspFilePath, 'utf-8');
      
      // Extract component name from file path
      const fileName = path.basename(jspFilePath, '.jsp');
      const componentName = this.toPascalCase(fileName);
      
      let reactComponent;
      
      if (this.options.useAI && this.llmProvider) {
        // Use AI-powered conversion
        reactComponent = await this.convertWithAI(jspContent, componentName);
      } else {
        // Fallback to rule-based conversion
        console.log('📝 Using rule-based conversion (AI not available)');
        reactComponent = await this.convertWithRules(jspContent, componentName);
      }
      
      // Write output file
      const outputFileName = `${componentName}.${this.options.outputFormat}`;
      const outputPath = path.join(outputDir, outputFileName);
      
      await fs.mkdir(outputDir, { recursive: true });
      await fs.writeFile(outputPath, reactComponent);
      
      console.log(`✅ Generated: ${outputPath}`);
      
      return {
        inputPath: jspFilePath,
        outputPath,
        componentName,
        success: true,
        method: this.options.useAI ? 'AI' : 'Rules'
      };
      
    } catch (error) {
      console.error(`❌ Failed to convert ${jspFilePath}:`, error.message);
      
      // Try fallback if AI conversion failed
      if (this.options.useAI && this.options.fallbackToRules) {
        console.log('🔄 Attempting rule-based fallback...');
        try {
          const jspContent = await fs.readFile(jspFilePath, 'utf-8');
          const fileName = path.basename(jspFilePath, '.jsp');
          const componentName = this.toPascalCase(fileName);
          
          const reactComponent = await this.convertWithRules(jspContent, componentName);
          
          const outputFileName = `${componentName}.${this.options.outputFormat}`;
          const outputPath = path.join(outputDir, outputFileName);
          
          await fs.mkdir(outputDir, { recursive: true });
          await fs.writeFile(outputPath, reactComponent);
          
          console.log(`✅ Generated (fallback): ${outputPath}`);
          
          return {
            inputPath: jspFilePath,
            outputPath,
            componentName,
            success: true,
            method: 'Rules (Fallback)'
          };
        } catch (fallbackError) {
          console.error(`❌ Fallback conversion also failed:`, fallbackError.message);
        }
      }
      
      return {
        inputPath: jspFilePath,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Convert JSP to React using AI
   */
  async convertWithAI(jspContent, componentName) {
    try {
      const result = await this.llmProvider.convertJSPToReact(jspContent, componentName, {
        useTypeScript: this.options.useTypeScript,
        temperature: 0.1
      });
      
      // Validate the result
      if (!result || result.trim().length === 0) {
        throw new Error('AI returned empty result');
      }
      
      // Basic validation - check if it looks like React code
      if (!result.includes('export') && !result.includes('function') && !result.includes('const')) {
        throw new Error('AI result does not appear to be valid React code');
      }
      
      return result;
    } catch (error) {
      console.error(`❌ AI conversion failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Convert JSP to React using rule-based approach (fallback)
   */
  async convertWithRules(jspContent, componentName) {
    // Import the original rule-based converter
    const JSPToReactConverter = require('./JSPToReactConverter');
    const ruleBasedConverter = new JSPToReactConverter(this.options);
    
    // Parse JSP content
    const parsedJsp = ruleBasedConverter.parseJSP(jspContent);
    
    // Generate React component
    return ruleBasedConverter.generateReactComponent(componentName, parsedJsp);
  }

  /**
   * Convert multiple JSP files with AI
   */
  async convertDirectory(sourceDir, outputDir) {
    console.log(`🔄 Converting JSP files from ${sourceDir} to ${outputDir}`);
    console.log(`🤖 AI Mode: ${this.options.useAI ? 'Enabled' : 'Disabled'}`);
    
    if (this.options.useAI && this.llmProvider) {
      const providerInfo = this.llmProvider.getProviderInfo();
      console.log(`🤖 Using ${providerInfo.name} (${providerInfo.model})`);
    }
    
    const results = [];
    
    try {
      // Find all JSP files
      const jspFiles = await this.findJSPFiles(sourceDir);
      
      console.log(`📁 Found ${jspFiles.length} JSP files to convert`);
      
      // Convert each file
      for (const jspFile of jspFiles) {
        const result = await this.convertFile(jspFile, outputDir);
        results.push(result);
        
        // Add a small delay between AI calls to avoid rate limiting
        if (this.options.useAI && jspFiles.length > 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
      
      // Generate summary
      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;
      const aiConverted = results.filter(r => r.success && r.method === 'AI').length;
      const ruleConverted = results.filter(r => r.success && r.method?.includes('Rules')).length;
      
      console.log(`\n📊 Conversion Summary:`);
      console.log(`✅ Successful: ${successful}`);
      console.log(`❌ Failed: ${failed}`);
      if (this.options.useAI) {
        console.log(`🤖 AI Converted: ${aiConverted}`);
        console.log(`📝 Rule-based: ${ruleConverted}`);
      }
      
      if (failed > 0) {
        console.log('\n❌ Failed conversions:');
        results.filter(r => !r.success).forEach(r => {
          console.log(`  • ${r.inputPath}: ${r.error}`);
        });
      }
      
      return results;
      
    } catch (error) {
      console.error('❌ Directory conversion failed:', error.message);
      throw error;
    }
  }

  /**
   * Find all JSP files in directory
   */
  async findJSPFiles(dir) {
    const jspFiles = [];
    
    async function scanDirectory(currentDir) {
      const entries = await fs.readdir(currentDir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(currentDir, entry.name);
        
        if (entry.isDirectory()) {
          await scanDirectory(fullPath);
        } else if (entry.isFile() && entry.name.endsWith('.jsp')) {
          jspFiles.push(fullPath);
        }
      }
    }
    
    await scanDirectory(dir);
    return jspFiles;
  }

  /**
   * Utility: Convert string to PascalCase
   */
  toPascalCase(str) {
    return str
      .split(/[-_\s]/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join('');
  }

  /**
   * Improve generated component with AI
   */
  async improveComponent(componentPath, issues = []) {
    if (!this.options.useAI || !this.llmProvider) {
      console.log('⚠️  AI improvement not available');
      return false;
    }

    try {
      const componentCode = await fs.readFile(componentPath, 'utf-8');
      const componentName = path.basename(componentPath, path.extname(componentPath));
      
      console.log(`🔧 Improving ${componentName} with AI...`);
      
      const improvedCode = await this.llmProvider.improveComponent(
        componentCode, 
        componentName, 
        issues
      );
      
      // Backup original
      const backupPath = componentPath + '.backup';
      await fs.writeFile(backupPath, componentCode);
      
      // Write improved version
      await fs.writeFile(componentPath, improvedCode);
      
      console.log(`✅ Component improved: ${componentPath}`);
      console.log(`📁 Backup saved: ${backupPath}`);
      
      return true;
    } catch (error) {
      console.error(`❌ Component improvement failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Get conversion statistics
   */
  getStats() {
    return {
      aiEnabled: this.options.useAI,
      provider: this.llmProvider?.getProviderInfo() || null,
      fallbackEnabled: this.options.fallbackToRules
    };
  }
}

module.exports = AIJSPToReactConverter;
