const fs = require('fs').promises;
const path = require('path');

/**
 * JSP to React Converter
 * 
 * Implements the core conversion logic from JSP files to React components
 * as outlined in Phase 3 of the README.md migration strategy.
 */
class JSPToReactConverter {
  constructor(options = {}) {
    this.options = {
      outputFormat: 'tsx', // tsx or jsx
      useTypeScript: true,
      preserveComments: true,
      generatePropTypes: false,
      ...options
    };
  }

  /**
   * Convert a single JSP file to React component
   */
  async convertFile(jspFilePath, outputDir) {
    try {
      console.log(`🔄 Converting: ${jspFilePath}`);
      
      // Read JSP file
      const jspContent = await fs.readFile(jspFilePath, 'utf-8');
      
      // Extract component name from file path
      const fileName = path.basename(jspFilePath, '.jsp');
      const componentName = this.toPascalCase(fileName);
      
      // Parse JSP content
      const parsedJsp = this.parseJSP(jspContent);
      
      // Convert to React component
      const reactComponent = this.generateReactComponent(componentName, parsedJsp);
      
      // Write output file
      const outputFileName = `${componentName}.${this.options.outputFormat}`;
      const outputPath = path.join(outputDir, outputFileName);
      
      await fs.mkdir(outputDir, { recursive: true });
      await fs.writeFile(outputPath, reactComponent);
      
      console.log(`✅ Generated: ${outputPath}`);
      
      return {
        inputPath: jspFilePath,
        outputPath,
        componentName,
        success: true
      };
      
    } catch (error) {
      console.error(`❌ Failed to convert ${jspFilePath}:`, error.message);
      return {
        inputPath: jspFilePath,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Parse JSP content and extract key components
   */
  parseJSP(content) {
    const result = {
      imports: [],
      title: '',
      cssLinks: [],
      bodyContent: '',
      jstlTags: [],
      elExpressions: [],
      javaScriptlets: []
    };

    // Extract title
    const titleMatch = content.match(/<title>(.*?)<\/title>/i);
    if (titleMatch) {
      result.title = titleMatch[1].trim();
    }

    // Extract CSS links
    const cssMatches = content.matchAll(/<link[^>]*rel=["']stylesheet["'][^>]*href=["']([^"']+)["'][^>]*>/gi);
    for (const match of cssMatches) {
      result.cssLinks.push(match[1]);
    }

    // Extract body content
    const bodyMatch = content.match(/<body[^>]*>(.*?)<\/body>/is);
    if (bodyMatch) {
      result.bodyContent = bodyMatch[1].trim();
    }

    // Extract JSTL tags
    const jstlMatches = content.matchAll(/<c:(\w+)[^>]*>(.*?)<\/c:\1>/gs);
    for (const match of jstlMatches) {
      result.jstlTags.push({
        tag: match[1],
        content: match[2],
        fullMatch: match[0]
      });
    }

    // Extract EL expressions
    const elMatches = content.matchAll(/\$\{([^}]+)\}/g);
    for (const match of elMatches) {
      result.elExpressions.push({
        expression: match[1],
        fullMatch: match[0]
      });
    }

    // Extract Java scriptlets
    const scriptletMatches = content.matchAll(/<%([^%]*)%>/g);
    for (const match of scriptletMatches) {
      result.javaScriptlets.push({
        code: match[1].trim(),
        fullMatch: match[0]
      });
    }

    return result;
  }

  /**
   * Generate React component from parsed JSP
   */
  generateReactComponent(componentName, parsedJsp) {
    const imports = this.generateImports(parsedJsp);
    const component = this.generateComponentBody(componentName, parsedJsp);
    
    return `${imports}\n\n${component}`;
  }

  /**
   * Generate import statements
   */
  generateImports(parsedJsp) {
    const imports = [];
    
    if (this.options.useTypeScript) {
      imports.push("import React from 'react';");
    } else {
      imports.push("import React from 'react';");
    }

    // Add CSS imports if any
    if (parsedJsp.cssLinks.length > 0) {
      parsedJsp.cssLinks.forEach(cssLink => {
        // Convert absolute paths to relative imports
        const cssPath = cssLink.startsWith('/') ? `.${cssLink}` : cssLink;
        imports.push(`import '${cssPath}';`);
      });
    }

    return imports.join('\n');
  }

  /**
   * Generate React component body
   */
  generateComponentBody(componentName, parsedJsp) {
    const props = this.options.useTypeScript ? ': React.FC' : '';
    const jsxContent = this.convertBodyToJSX(parsedJsp.bodyContent);
    
    return `export default function ${componentName}()${props} {
  return (
    <div>
      {/* Converted from JSP */}
${this.indentContent(jsxContent, 6)}
    </div>
  );
}`;
  }

  /**
   * Convert JSP body content to JSX
   */
  convertBodyToJSX(bodyContent) {
    let jsx = bodyContent;

    // Basic HTML to JSX conversions
    jsx = jsx.replace(/class=/g, 'className=');
    jsx = jsx.replace(/for=/g, 'htmlFor=');
    
    // Convert JSTL c:forEach to map
    jsx = jsx.replace(
      /<c:forEach\s+items="\$\{([^}]+)\}"\s+var="([^"]+)"[^>]*>(.*?)<\/c:forEach>/gs,
      (match, items, varName, content) => {
        const mappedContent = content.replace(new RegExp(`\\$\\{${varName}\\.`, 'g'), '{item.');
        return `{${items}.map((item, index) => (
        <div key={index}>
${this.indentContent(mappedContent.trim(), 10)}
        </div>
      ))}`;
      }
    );

    // Convert JSTL c:if to conditional rendering
    jsx = jsx.replace(
      /<c:if\s+test="\$\{([^}]+)\}"[^>]*>(.*?)<\/c:if>/gs,
      (match, condition, content) => {
        return `{${condition} && (
        <div>
${this.indentContent(content.trim(), 10)}
        </div>
      )}`;
      }
    );

    // Convert EL expressions to JSX expressions
    jsx = jsx.replace(/\$\{([^}]+)\}/g, '{$1}');

    // Handle form elements
    jsx = jsx.replace(/<input([^>]*)\s*\/?>/g, '<input$1 />');
    jsx = jsx.replace(/<br\s*\/?>/g, '<br />');

    return jsx;
  }

  /**
   * Utility: Convert string to PascalCase
   */
  toPascalCase(str) {
    return str
      .split(/[-_\s]/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join('');
  }

  /**
   * Utility: Indent content
   */
  indentContent(content, spaces) {
    const indent = ' '.repeat(spaces);
    return content
      .split('\n')
      .map(line => line.trim() ? indent + line : line)
      .join('\n');
  }

  /**
   * Convert multiple JSP files
   */
  async convertDirectory(sourceDir, outputDir) {
    console.log(`🔄 Converting JSP files from ${sourceDir} to ${outputDir}`);
    
    const results = [];
    
    try {
      // Find all JSP files
      const jspFiles = await this.findJSPFiles(sourceDir);
      
      console.log(`📁 Found ${jspFiles.length} JSP files to convert`);
      
      // Convert each file
      for (const jspFile of jspFiles) {
        const result = await this.convertFile(jspFile, outputDir);
        results.push(result);
      }
      
      // Generate summary
      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;
      
      console.log(`\n📊 Conversion Summary:`);
      console.log(`✅ Successful: ${successful}`);
      console.log(`❌ Failed: ${failed}`);
      
      if (failed > 0) {
        console.log('\n❌ Failed conversions:');
        results.filter(r => !r.success).forEach(r => {
          console.log(`  • ${r.inputPath}: ${r.error}`);
        });
      }
      
      return results;
      
    } catch (error) {
      console.error('❌ Directory conversion failed:', error.message);
      throw error;
    }
  }

  /**
   * Find all JSP files in directory
   */
  async findJSPFiles(dir) {
    const jspFiles = [];
    
    async function scanDirectory(currentDir) {
      const entries = await fs.readdir(currentDir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(currentDir, entry.name);
        
        if (entry.isDirectory()) {
          await scanDirectory(fullPath);
        } else if (entry.isFile() && entry.name.endsWith('.jsp')) {
          jspFiles.push(fullPath);
        }
      }
    }
    
    await scanDirectory(dir);
    return jspFiles;
  }
}

module.exports = JSPToReactConverter;
