const path = require('path');
const WebXmlParser = require('./parsers/WebXmlParser');
const JavaAnnotationScanner = require('./parsers/JavaAnnotationScanner');
const StaticResourceMapper = require('./parsers/StaticResourceMapper');
const RouteManifestGenerator = require('./RouteManifestGenerator');
const ProcessManager = require('../process/ProcessManager');
const DynamicPageAnalyzer = require('../process/DynamicPageAnalyzer');

/**
 * Main RouteDiscovery class that orchestrates the discovery process
 */
class RouteDiscovery {
  constructor(options = {}) {
    this.options = {
      includeStaticResources: true,
      includeWebXml: true,
      includeAnnotations: true,
      includeDynamicAnalysis: false, // 新增：是否包含动态分析
      minConfidence: 0.0,
      deduplicateRoutes: true,
      dynamicAnalysisTimeout: 120000, // 动态分析超时时间
      savePageContents: true, // 是否保存页面内容
      outputDir: './output/dynamic-analysis', // 输出目录
      ...options
    };

    this.webXmlParser = new WebXmlParser();
    this.annotationScanner = new JavaAnnotationScanner();
    this.staticMapper = new StaticResourceMapper();
    this.manifestGenerator = new RouteManifestGenerator();

    // 进程管理和动态分析组件
    this.processManager = new ProcessManager();
    this.pageAnalyzer = new DynamicPageAnalyzer({
      headless: true,
      timeout: 30000
    });

    this.runningApplication = null; // 当前运行的应用信息
  }

  /**
   * Discover all routes in a JSP/Java web application
   * @param {string} projectPath - Path to the web application project
   * @returns {Promise<RouteManifest>} Complete route manifest
   */
  async discoverRoutes(projectPath) {
    try {
      console.log(`Starting route discovery for project: ${projectPath}`);
      
      // Clear any previous results
      this.manifestGenerator.clear();

      // Discover routes from different sources
      await this.discoverFromWebXml(projectPath);
      await this.discoverFromAnnotations(projectPath);
      await this.discoverFromStaticResources(projectPath);

      // 动态分析（如果启用）
      if (this.options.includeDynamicAnalysis) {
        await this.discoverFromDynamicAnalysis(projectPath);
      }

      // Post-process routes
      this.postProcessRoutes();

      // Generate final manifest
      const manifest = this.manifestGenerator.generateManifest(projectPath);
      
      console.log(`Route discovery completed. Found ${manifest.routes.length} routes.`);
      return manifest;
    } catch (error) {
      console.error('Route discovery failed:', error.message);
      throw error;
    } finally {
      // 确保清理资源
      await this.cleanup();
    }
  }

  /**
   * Discover routes from web.xml configuration
   * @param {string} projectPath - Project path
   */
  async discoverFromWebXml(projectPath) {
    if (!this.options.includeWebXml) {
      return;
    }

    try {
      const webXmlPath = path.join(projectPath, 'src/main/webapp/WEB-INF/web.xml');
      console.log(`Scanning web.xml: ${webXmlPath}`);
      
      const servletMappings = await this.webXmlParser.parseWebXml(webXmlPath);
      const routes = this.webXmlParser.convertToRoutes(servletMappings);
      
      this.manifestGenerator.addRoutes(routes, 'web.xml');
      console.log(`Found ${routes.length} routes from web.xml`);
    } catch (error) {
      console.warn(`Warning: Could not parse web.xml: ${error.message}`);
    }
  }

  /**
   * Discover routes from Java annotations
   * @param {string} projectPath - Project path
   */
  async discoverFromAnnotations(projectPath) {
    if (!this.options.includeAnnotations) {
      return;
    }

    try {
      const javaSourcePath = path.join(projectPath, 'src/main/java');
      console.log(`Scanning Java annotations in: ${javaSourcePath}`);
      
      const routes = await this.annotationScanner.scanJavaFiles(javaSourcePath);
      
      this.manifestGenerator.addRoutes(routes, 'annotations');
      console.log(`Found ${routes.length} routes from annotations`);
    } catch (error) {
      console.warn(`Warning: Could not scan Java annotations: ${error.message}`);
    }
  }

  /**
   * Discover routes from static resources
   * @param {string} projectPath - Project path
   */
  async discoverFromStaticResources(projectPath) {
    if (!this.options.includeStaticResources) {
      return;
    }

    try {
      const webappPath = path.join(projectPath, 'src/main/webapp');
      console.log(`Scanning static resources in: ${webappPath}`);
      
      const routes = await this.staticMapper.scanStaticResources(webappPath);
      
      this.manifestGenerator.addRoutes(routes, 'static');
      console.log(`Found ${routes.length} static resource routes`);
    } catch (error) {
      console.warn(`Warning: Could not scan static resources: ${error.message}`);
    }
  }

  /**
   * Post-process discovered routes
   */
  postProcessRoutes() {
    // Filter by minimum confidence
    if (this.options.minConfidence > 0) {
      const originalCount = this.manifestGenerator.routes.length;
      this.manifestGenerator.routes = this.manifestGenerator.routes.filter(
        route => route.confidence >= this.options.minConfidence
      );
      const filteredCount = originalCount - this.manifestGenerator.routes.length;
      if (filteredCount > 0) {
        console.log(`Filtered out ${filteredCount} routes below confidence threshold`);
      }
    }

    // Remove duplicates if requested
    if (this.options.deduplicateRoutes) {
      const originalCount = this.manifestGenerator.routes.length;
      this.manifestGenerator.deduplicateRoutes();
      const duplicatesRemoved = originalCount - this.manifestGenerator.routes.length;
      if (duplicatesRemoved > 0) {
        console.log(`Removed ${duplicatesRemoved} duplicate routes`);
      }
    }

    // Sort routes by path for consistent output
    this.manifestGenerator.sortRoutes('path', 'asc');
  }

  /**
   * Get routes with optional filtering
   * @param {Object} filters - Filter criteria
   * @returns {Route[]} Filtered routes
   */
  getRoutes(filters = {}) {
    return this.manifestGenerator.getRoutes(filters);
  }

  /**
   * Export route manifest to file
   * @param {string} outputPath - Output file path
   * @param {string} projectPath - Source project path
   * @returns {Promise<void>}
   */
  async exportManifest(outputPath, projectPath) {
    return this.manifestGenerator.exportToFile(outputPath, projectPath);
  }

  /**
   * Generate a human-readable summary
   * @returns {string} Summary text
   */
  generateSummary() {
    return this.manifestGenerator.generateSummary();
  }

  /**
   * Analyze route patterns and suggest React Router structure
   * @returns {Object} Analysis and suggestions
   */
  analyzeForReactRouter() {
    const routes = this.manifestGenerator.routes;
    const analysis = {
      dynamicRoutes: [],
      staticRoutes: [],
      nestedRoutes: [],
      suggestions: []
    };

    routes.forEach(route => {
      // Skip static resources for React Router analysis
      if (route.type === 'static' || route.type === 'image' || route.type === 'stylesheet') {
        analysis.staticRoutes.push(route);
        return;
      }

      // Detect dynamic routes (with parameters)
      if (route.path.includes('/:') || route.path.match(/\/\d+/) || route.path.includes('/*')) {
        analysis.dynamicRoutes.push(route);
      } else {
        analysis.staticRoutes.push(route);
      }

      // Detect potential nested routes
      const pathSegments = route.path.split('/').filter(segment => segment.length > 0);
      if (pathSegments.length > 2) {
        analysis.nestedRoutes.push(route);
      }
    });

    // Generate suggestions
    if (analysis.dynamicRoutes.length > 0) {
      analysis.suggestions.push('Consider using React Router dynamic segments for parameterized routes');
    }

    if (analysis.nestedRoutes.length > 0) {
      analysis.suggestions.push('Consider organizing nested routes with React Router nested routing');
    }

    if (analysis.staticRoutes.length > 10) {
      analysis.suggestions.push('Consider grouping related static routes under common layouts');
    }

    return analysis;
  }

  /**
   * 通过动态分析发现路由
   * @param {string} projectPath - 项目路径
   */
  async discoverFromDynamicAnalysis(projectPath) {
    try {
      console.log('Starting dynamic analysis...');

      // 启动应用
      this.runningApplication = await this.processManager.startApplication(projectPath, {
        timeout: this.options.dynamicAnalysisTimeout
      });

      console.log(`Application started at: ${this.runningApplication.baseUrl}`);

      // 等待应用完全启动
      await this._waitForApplicationReady(this.runningApplication.baseUrl);

      // 获取已发现的路由用于动态测试
      const staticRoutes = this.manifestGenerator.routes.filter(route =>
        route.type !== 'static' && route.method === 'GET'
      );

      // 构建要测试的URL列表
      const urlsToTest = this._buildTestUrls(this.runningApplication.baseUrl, staticRoutes);

      console.log(`Testing ${urlsToTest.length} URLs dynamically...`);

      // 批量分析页面
      const analysisResults = await this.pageAnalyzer.analyzePages(urlsToTest, {
        concurrent: 2,
        delay: 1000,
        screenshot: false
      });

      // 处理分析结果
      const dynamicRoutes = this._processDynamicAnalysisResults(analysisResults);

      if (dynamicRoutes.length > 0) {
        this.manifestGenerator.addRoutes(dynamicRoutes, 'dynamic');
        console.log(`Found ${dynamicRoutes.length} routes from dynamic analysis`);
      }

      // 保存页面内容（如果启用）
      if (this.options.savePageContents) {
        await this.pageAnalyzer.saveAllPageContents(analysisResults, this.options.outputDir);
      }

    } catch (error) {
      console.warn(`Dynamic analysis failed: ${error.message}`);
    }
  }

  /**
   * 等待应用就绪
   * @param {string} baseUrl - 应用基础URL
   * @param {number} maxRetries - 最大重试次数
   */
  async _waitForApplicationReady(baseUrl, maxRetries = 30) {
    for (let i = 0; i < maxRetries; i++) {
      try {
        const isReady = await this.pageAnalyzer.isApplicationAccessible(baseUrl, 5000);
        if (isReady) {
          console.log('Application is ready for dynamic analysis');
          return;
        }
      } catch (error) {
        // 忽略错误，继续重试
      }

      console.log(`Waiting for application to be ready... (${i + 1}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    throw new Error('Application failed to become ready within timeout');
  }

  /**
   * 构建测试URL列表
   * @param {string} baseUrl - 应用基础URL
   * @param {Array} routes - 路由列表
   * @returns {Array<string>} URL列表
   */
  _buildTestUrls(baseUrl, routes) {
    const urls = new Set();

    // 添加根路径
    urls.add(baseUrl);
    urls.add(`${baseUrl}/`);

    // 添加发现的路由
    routes.forEach(route => {
      if (route.path && route.path !== '/') {
        // 处理参数化路径
        let testPath = route.path;

        // 替换常见的路径参数
        testPath = testPath.replace(/\{[^}]+\}/g, '1'); // {id} -> 1
        testPath = testPath.replace(/:\w+/g, '1'); // :id -> 1
        testPath = testPath.replace(/\*/g, 'test'); // * -> test

        const fullUrl = `${baseUrl}${testPath}`;
        urls.add(fullUrl);
      }
    });

    // 添加常见的端点
    const commonPaths = [
      '/index',
      '/home',
      '/login',
      '/admin',
      '/api',
      '/health',
      '/status'
    ];

    commonPaths.forEach(path => {
      urls.add(`${baseUrl}${path}`);
    });

    return Array.from(urls);
  }

  /**
   * 处理动态分析结果
   * @param {Array} analysisResults - 分析结果
   * @returns {Array} 动态路由列表
   */
  _processDynamicAnalysisResults(analysisResults) {
    const dynamicRoutes = [];

    analysisResults.forEach(result => {
      if (result.success && result.result) {
        const { url, status, pageInfo } = result.result;

        // 只处理成功的响应
        if (status >= 200 && status < 400) {
          const urlObj = new URL(url);
          const route = {
            path: urlObj.pathname,
            method: 'GET',
            type: 'dynamic',
            source: 'dynamic-analysis',
            confidence: this._calculateDynamicConfidence(status, pageInfo),
            metadata: {
              status,
              title: pageInfo.title,
              formsCount: pageInfo.forms ? pageInfo.forms.length : 0,
              linksCount: pageInfo.links ? pageInfo.links.length : 0,
              scriptsCount: pageInfo.scripts ? pageInfo.scripts.length : 0,
              discoveredAt: new Date().toISOString()
            }
          };

          dynamicRoutes.push(route);
        }
      }
    });

    return dynamicRoutes;
  }

  /**
   * 计算动态分析的置信度
   * @param {number} status - HTTP状态码
   * @param {Object} pageInfo - 页面信息
   * @returns {number} 置信度
   */
  _calculateDynamicConfidence(status, pageInfo) {
    let confidence = 0.5; // 基础置信度

    // 根据状态码调整
    if (status === 200) {
      confidence += 0.3;
    } else if (status >= 200 && status < 300) {
      confidence += 0.2;
    }

    // 根据页面内容调整
    if (pageInfo.title && pageInfo.title.trim()) {
      confidence += 0.1;
    }

    if (pageInfo.forms && pageInfo.forms.length > 0) {
      confidence += 0.1;
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      // 停止运行的应用
      if (this.runningApplication) {
        await this.processManager.stopApplication(this.runningApplication.id);
        this.runningApplication = null;
      }

      // 关闭浏览器
      await this.pageAnalyzer.close();

      console.log('Resources cleaned up successfully');
    } catch (error) {
      console.warn('Error during cleanup:', error.message);
    }
  }

  /**
   * 获取运行中的应用信息
   * @returns {Object|null} 应用信息
   */
  getRunningApplication() {
    return this.runningApplication;
  }

  /**
   * 手动启动应用进行分析
   * @param {string} projectPath - 项目路径
   * @returns {Promise<Object>} 应用信息
   */
  async startApplicationForAnalysis(projectPath) {
    if (this.runningApplication) {
      throw new Error('Application is already running');
    }

    this.runningApplication = await this.processManager.startApplication(projectPath, {
      timeout: this.options.dynamicAnalysisTimeout
    });

    return this.runningApplication;
  }

  /**
   * 手动停止应用
   */
  async stopApplication() {
    if (!this.runningApplication) {
      throw new Error('No application is currently running');
    }

    await this.processManager.stopApplication(this.runningApplication.id);
    this.runningApplication = null;
  }
}

module.exports = RouteDiscovery;
