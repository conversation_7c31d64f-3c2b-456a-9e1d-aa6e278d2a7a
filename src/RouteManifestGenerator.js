const fs = require('fs');
const path = require('path');

/**
 * Generator for route manifests with statistics and metadata
 */
class RouteManifestGenerator {
  constructor() {
    this.routes = [];
    this.metadata = {
      timestamp: null,
      sourceProject: null,
      stats: {}
    };
  }

  /**
   * Add routes to the manifest
   * @param {Route[]} routes - Array of routes to add
   * @param {string} source - Source identifier
   */
  addRoutes(routes, source = 'unknown') {
    routes.forEach(route => {
      // Ensure route has required fields
      const normalizedRoute = this.normalizeRoute(route, source);
      this.routes.push(normalizedRoute);
    });
  }

  /**
   * Normalize route object to ensure consistency
   * @param {Route} route - Route to normalize
   * @param {string} defaultSource - Default source if not specified
   * @returns {Route} Normalized route
   */
  normalizeRoute(route, defaultSource) {
    return {
      path: route.path || '/',
      method: route.method || 'GET',
      type: route.type || 'unknown',
      source: route.source || defaultSource,
      confidence: typeof route.confidence === 'number' ? route.confidence : 0.5,
      metadata: route.metadata || {}
    };
  }

  /**
   * Generate the complete route manifest
   * @param {string} sourceProject - Source project path
   * @returns {RouteManifest} Complete route manifest
   */
  generateManifest(sourceProject) {
    this.metadata.timestamp = new Date().toISOString();
    this.metadata.sourceProject = sourceProject;
    this.metadata.stats = this.calculateStats();

    return {
      routes: this.routes,
      metadata: this.metadata
    };
  }

  /**
   * Calculate statistics about the discovered routes
   * @returns {Object} Statistics object
   */
  calculateStats() {
    const stats = {
      total: this.routes.length,
      byType: {},
      bySource: {},
      byMethod: {},
      byConfidence: {
        high: 0,    // >= 0.8
        medium: 0,  // 0.5 - 0.8
        low: 0      // < 0.5
      },
      duplicates: 0,
      averageConfidence: 0
    };

    // Count by type, source, and method
    this.routes.forEach(route => {
      // By type
      stats.byType[route.type] = (stats.byType[route.type] || 0) + 1;
      
      // By source
      stats.bySource[route.source] = (stats.bySource[route.source] || 0) + 1;
      
      // By method
      stats.byMethod[route.method] = (stats.byMethod[route.method] || 0) + 1;
      
      // By confidence
      if (route.confidence >= 0.8) {
        stats.byConfidence.high++;
      } else if (route.confidence >= 0.5) {
        stats.byConfidence.medium++;
      } else {
        stats.byConfidence.low++;
      }
    });

    // Calculate average confidence
    if (this.routes.length > 0) {
      const totalConfidence = this.routes.reduce((sum, route) => sum + route.confidence, 0);
      stats.averageConfidence = totalConfidence / this.routes.length;
    }

    // Detect duplicates
    stats.duplicates = this.detectDuplicates();

    return stats;
  }

  /**
   * Detect duplicate routes
   * @returns {number} Number of duplicate routes
   */
  detectDuplicates() {
    const seen = new Set();
    let duplicates = 0;

    this.routes.forEach(route => {
      const key = `${route.path}:${route.method}`;
      if (seen.has(key)) {
        duplicates++;
      } else {
        seen.add(key);
      }
    });

    return duplicates;
  }

  /**
   * Get routes filtered by criteria
   * @param {Object} filters - Filter criteria
   * @returns {Route[]} Filtered routes
   */
  getRoutes(filters = {}) {
    let filtered = [...this.routes];

    if (filters.type) {
      filtered = filtered.filter(route => route.type === filters.type);
    }

    if (filters.source) {
      filtered = filtered.filter(route => route.source === filters.source);
    }

    if (filters.method) {
      filtered = filtered.filter(route => route.method === filters.method);
    }

    if (filters.minConfidence !== undefined) {
      filtered = filtered.filter(route => route.confidence >= filters.minConfidence);
    }

    if (filters.pathPattern) {
      const regex = new RegExp(filters.pathPattern);
      filtered = filtered.filter(route => regex.test(route.path));
    }

    return filtered;
  }

  /**
   * Sort routes by specified criteria
   * @param {string} sortBy - Sort criteria (path, confidence, type, source)
   * @param {string} order - Sort order (asc, desc)
   */
  sortRoutes(sortBy = 'path', order = 'asc') {
    this.routes.sort((a, b) => {
      let aVal = a[sortBy];
      let bVal = b[sortBy];

      // Handle string comparison
      if (typeof aVal === 'string' && typeof bVal === 'string') {
        aVal = aVal.toLowerCase();
        bVal = bVal.toLowerCase();
      }

      let comparison = 0;
      if (aVal < bVal) {
        comparison = -1;
      } else if (aVal > bVal) {
        comparison = 1;
      }

      return order === 'desc' ? -comparison : comparison;
    });
  }

  /**
   * Remove duplicate routes, keeping the one with highest confidence
   */
  deduplicateRoutes() {
    const routeMap = new Map();

    this.routes.forEach(route => {
      const key = `${route.path}:${route.method}`;
      const existing = routeMap.get(key);

      if (!existing || route.confidence > existing.confidence) {
        routeMap.set(key, route);
      }
    });

    this.routes = Array.from(routeMap.values());
  }

  /**
   * Export manifest to JSON file
   * @param {string} outputPath - Output file path
   * @param {string} sourceProject - Source project path
   * @returns {Promise<void>}
   */
  async exportToFile(outputPath, sourceProject) {
    try {
      const manifest = this.generateManifest(sourceProject);
      const jsonContent = JSON.stringify(manifest, null, 2);
      
      // Ensure output directory exists
      const outputDir = path.dirname(outputPath);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      fs.writeFileSync(outputPath, jsonContent, 'utf8');
    } catch (error) {
      throw new Error(`Failed to export manifest: ${error.message}`);
    }
  }

  /**
   * Import manifest from JSON file
   * @param {string} filePath - Input file path
   * @returns {Promise<RouteManifest>}
   */
  async importFromFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const manifest = JSON.parse(content);
      
      this.routes = manifest.routes || [];
      this.metadata = manifest.metadata || {};
      
      return manifest;
    } catch (error) {
      throw new Error(`Failed to import manifest: ${error.message}`);
    }
  }

  /**
   * Generate a summary report of the routes
   * @returns {string} Human-readable summary
   */
  generateSummary() {
    const stats = this.calculateStats();
    
    let summary = `Route Discovery Summary\n`;
    summary += `========================\n\n`;
    summary += `Total Routes: ${stats.total}\n\n`;
    
    summary += `By Type:\n`;
    Object.entries(stats.byType).forEach(([type, count]) => {
      summary += `  ${type}: ${count}\n`;
    });
    
    summary += `\nBy Source:\n`;
    Object.entries(stats.bySource).forEach(([source, count]) => {
      summary += `  ${source}: ${count}\n`;
    });
    
    summary += `\nBy HTTP Method:\n`;
    Object.entries(stats.byMethod).forEach(([method, count]) => {
      summary += `  ${method}: ${count}\n`;
    });
    
    summary += `\nConfidence Distribution:\n`;
    summary += `  High (≥0.8): ${stats.byConfidence.high}\n`;
    summary += `  Medium (0.5-0.8): ${stats.byConfidence.medium}\n`;
    summary += `  Low (<0.5): ${stats.byConfidence.low}\n`;
    summary += `  Average: ${stats.averageConfidence.toFixed(2)}\n`;
    
    if (stats.duplicates > 0) {
      summary += `\nDuplicates Found: ${stats.duplicates}\n`;
    }
    
    return summary;
  }

  /**
   * Clear all routes and reset metadata
   */
  clear() {
    this.routes = [];
    this.metadata = {
      timestamp: null,
      sourceProject: null,
      stats: {}
    };
  }
}

module.exports = RouteManifestGenerator;
