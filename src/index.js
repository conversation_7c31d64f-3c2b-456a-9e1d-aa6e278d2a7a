const RouteDiscovery = require('./analysis/RouteDiscovery');

// Export main classes for library usage
module.exports = {
  RouteDiscovery,
  WebXmlParser: require('./analysis/parsers/WebXmlParser'),
  JavaAnnotationScanner: require('./analysis/parsers/JavaAnnotationScanner'),
  StaticResourceMapper: require('./analysis/parsers/StaticResourceMapper'),
  RouteManifestGenerator: require('./analysis/RouteManifestGenerator')
};

// CLI functionality if run directly
if (require.main === module) {
  // Redirect to CLI entry point
  require('./cli');
}
