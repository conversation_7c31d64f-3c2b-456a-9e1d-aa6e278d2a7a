const fs = require('fs');
const path = require('path');
const { XMLParser } = require('fast-xml-parser');

/**
 * Parser for web.xml files to extract servlet mappings and configurations
 */
class WebXmlParser {
  constructor() {
    this.parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: '@_',
      textNodeName: '#text',
      parseAttributeValue: true,
      trimValues: true
    });
  }

  /**
   * Parse web.xml file and extract servlet mappings
   * @param {string} webXmlPath - Path to web.xml file
   * @returns {Promise<ServletMapping[]>} Array of servlet mappings
   */
  async parseWebXml(webXmlPath) {
    try {
      if (!fs.existsSync(webXmlPath)) {
        throw new Error(`web.xml not found at: ${webXmlPath}`);
      }

      const xmlContent = fs.readFileSync(webXmlPath, 'utf8');
      const parsed = this.parser.parse(xmlContent);
      
      return this.extractServletMappings(parsed);
    } catch (error) {
      throw new Error(`Failed to parse web.xml: ${error.message}`);
    }
  }

  /**
   * Extract servlet mappings from parsed XML
   * @param {Object} parsedXml - Parsed XML object
   * @returns {ServletMapping[]} Array of servlet mappings
   */
  extractServletMappings(parsedXml) {
    const webApp = parsedXml['web-app'] || parsedXml.webapp;
    if (!webApp) {
      return [];
    }

    const servlets = this.normalizeArray(webApp.servlet || []);
    const servletMappings = this.normalizeArray(webApp['servlet-mapping'] || []);

    // Create a map of servlet names to their classes
    const servletMap = new Map();
    servlets.forEach(servlet => {
      const name = servlet['servlet-name'];
      const className = servlet['servlet-class'];
      if (name && className) {
        servletMap.set(name, className);
      }
    });

    // Process servlet mappings
    const mappings = [];
    servletMappings.forEach(mapping => {
      const servletName = mapping['servlet-name'];
      const urlPatterns = this.normalizeArray(mapping['url-pattern'] || []);
      const servletClass = servletMap.get(servletName);

      if (servletName && urlPatterns.length > 0) {
        mappings.push({
          servletName,
          urlPatterns,
          servletClass: servletClass || 'unknown'
        });
      }
    });

    return mappings;
  }

  /**
   * Normalize single items or arrays to always return arrays
   * @param {*} item - Item to normalize
   * @returns {Array} Normalized array
   */
  normalizeArray(item) {
    if (!item) return [];
    return Array.isArray(item) ? item : [item];
  }

  /**
   * Convert servlet mappings to routes
   * @param {ServletMapping[]} servletMappings - Servlet mappings
   * @returns {Route[]} Array of routes
   */
  convertToRoutes(servletMappings) {
    const routes = [];

    servletMappings.forEach(mapping => {
      mapping.urlPatterns.forEach(pattern => {
        routes.push({
          path: this.normalizeUrlPattern(pattern),
          method: 'ALL', // web.xml doesn't specify HTTP methods
          type: 'servlet',
          source: 'web.xml',
          confidence: 0.9, // High confidence for explicit mappings
          metadata: {
            servletName: mapping.servletName,
            servletClass: mapping.servletClass,
            originalPattern: pattern
          }
        });
      });
    });

    return routes;
  }

  /**
   * Normalize URL patterns for consistent format
   * @param {string} pattern - URL pattern from web.xml
   * @returns {string} Normalized pattern
   */
  normalizeUrlPattern(pattern) {
    // Remove leading slash if present for consistency
    let normalized = pattern.startsWith('/') ? pattern.substring(1) : pattern;
    
    // Handle wildcard patterns
    if (normalized.endsWith('/*')) {
      normalized = normalized.substring(0, normalized.length - 2) + '/**';
    }
    
    return normalized === '' ? '/' : '/' + normalized;
  }
}

module.exports = WebXmlParser;
