const fs = require('fs');
const path = require('path');
const JavaAnnotationScanner = require('../parsers/JavaAnnotationScanner');

describe('JavaAnnotationScanner', () => {
  let scanner;
  let tempDir;

  beforeEach(() => {
    scanner = new JavaAnnotationScanner();
    tempDir = path.join(__dirname, 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
  });

  afterEach(() => {
    // Clean up temp files
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });

  describe('scanFile', () => {
    test('should scan @WebServlet annotation', async () => {
      const javaContent = `package com.example;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;

@WebServlet(urlPatterns = {"/posts/*", "/blog"})
public class BlogController extends HttpServlet {
    // servlet implementation
}`;

      const javaPath = path.join(tempDir, 'BlogController.java');
      fs.writeFileSync(javaPath, javaContent);

      const routes = await scanner.scanFile(javaPath);

      expect(routes).toHaveLength(2);
      expect(routes[0]).toEqual({
        path: '/posts/**',
        method: 'ALL',
        type: 'servlet',
        source: 'annotation',
        confidence: 0.95,
        metadata: {
          className: 'BlogController',
          annotation: '@WebServlet',
          filePath: expect.stringContaining('BlogController.java'),
          originalPattern: '/posts/*'
        }
      });
      expect(routes[1]).toEqual({
        path: '/blog',
        method: 'ALL',
        type: 'servlet',
        source: 'annotation',
        confidence: 0.95,
        metadata: {
          className: 'BlogController',
          annotation: '@WebServlet',
          filePath: expect.stringContaining('BlogController.java'),
          originalPattern: '/blog'
        }
      });
    });

    test('should scan Spring @RequestMapping annotation', async () => {
      const javaContent = `package com.example;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
public class ApiController {
    
    @RequestMapping("/users")
    public String getUsers() {
        return "users";
    }
}`;

      const javaPath = path.join(tempDir, 'ApiController.java');
      fs.writeFileSync(javaPath, javaContent);

      const routes = await scanner.scanFile(javaPath);

      expect(routes).toHaveLength(2);
      expect(routes.some(r => r.path === '/api')).toBe(true);
      expect(routes.some(r => r.path === '/users')).toBe(true);
    });

    test('should scan Spring HTTP method annotations', async () => {
      const javaContent = `package com.example;

import org.springframework.web.bind.annotation.*;

@RestController
public class UserController {
    
    @GetMapping("/users")
    public String getUsers() { return ""; }
    
    @PostMapping("/users")
    public String createUser() { return ""; }
    
    @PutMapping("/users/{id}")
    public String updateUser() { return ""; }
    
    @DeleteMapping("/users/{id}")
    public String deleteUser() { return ""; }
}`;

      const javaPath = path.join(tempDir, 'UserController.java');
      fs.writeFileSync(javaPath, javaContent);

      const routes = await scanner.scanFile(javaPath);

      expect(routes).toHaveLength(4);
      expect(routes.find(r => r.path === '/users' && r.method === 'GET')).toBeDefined();
      expect(routes.find(r => r.path === '/users' && r.method === 'POST')).toBeDefined();
      expect(routes.find(r => r.path === '/users/{id}' && r.method === 'PUT')).toBeDefined();
      expect(routes.find(r => r.path === '/users/{id}' && r.method === 'DELETE')).toBeDefined();
    });

    test('should handle file read errors gracefully', async () => {
      const routes = await scanner.scanFile('/non/existent/file.java');
      expect(routes).toHaveLength(0);
    });
  });

  describe('extractUrlPatterns', () => {
    test('should extract URL patterns from various formats', () => {
      expect(scanner.extractUrlPatterns('urlPatterns = {"/api/*", "/rest/*"}'))
        .toEqual(['/api/*', '/rest/*']);
      
      expect(scanner.extractUrlPatterns('urlPatterns = "/single"'))
        .toEqual(['/single']);
      
      expect(scanner.extractUrlPatterns('"/shorthand"'))
        .toEqual(['/shorthand']);
      
      expect(scanner.extractUrlPatterns('value = {"/value1", "/value2"}'))
        .toEqual(['/value1', '/value2']);
    });
  });

  describe('extractPaths', () => {
    test('should extract paths from Spring annotations', () => {
      expect(scanner.extractPaths('path = {"/api", "/rest"}'))
        .toEqual(['/api', '/rest']);
      
      expect(scanner.extractPaths('value = "/single"'))
        .toEqual(['/single']);
      
      expect(scanner.extractPaths('"/direct"'))
        .toEqual(['/direct']);
    });
  });

  describe('extractClassName', () => {
    test('should extract class name from Java content', () => {
      const content = `package com.example;
      
public class TestController {
}`;
      expect(scanner.extractClassName(content, '/path/TestController.java'))
        .toBe('TestController');
    });

    test('should fallback to filename if class not found', () => {
      const content = `// no definition found`;
      expect(scanner.extractClassName(content, '/path/TestController.java'))
        .toBe('TestController');
    });
  });

  describe('normalizeUrlPattern', () => {
    test('should normalize URL patterns', () => {
      expect(scanner.normalizeUrlPattern('/api/*')).toBe('/api/**');
      expect(scanner.normalizeUrlPattern('api')).toBe('/api');
      expect(scanner.normalizeUrlPattern('')).toBe('/');
      expect(scanner.normalizeUrlPattern(null)).toBe('/');
    });
  });
});
