const ProcessManager = require('../../process/ProcessManager');
const ApplicationDetector = require('../../process/ApplicationDetector');
const { spawn } = require('cross-spawn');

// Mock dependencies
jest.mock('cross-spawn');
jest.mock('../../process/ApplicationDetector');

describe('ProcessManager', () => {
    let processManager;
    let mockProcess;
    let mockDetector;

    beforeEach(() => {
        
        // Mock child process
        mockProcess = {
            pid: 12345,
            stdout: { on: jest.fn() },
            stderr: { on: jest.fn() },
            on: jest.fn(),
            kill: jest.fn()
        };

        spawn.mockReturnValue(mockProcess);

        // Mock detector
        mockDetector = {
            detectApplicationType: jest.fn(),
            getBaseUrl: jest.fn(),
            supportedTypes: {
                MAVEN: 'maven',
                GRADLE: 'gradle',
                UNKNOWN: 'unknown'
            }
        };

        ApplicationDetector.mockImplementation(() => mockDetector);

        // 重新创建ProcessManager实例以使用mock
        processManager = new ProcessManager();
        
        jest.clearAllMocks();
    });

    describe('startApplication', () => {
        test('should start Maven application successfully', async () => {
            const projectPath = '/test/maven-project';
            const detection = {
                type: 'maven',
                startCommand: 'mvn spring-boot:run',
                port: 8080,
                contextPath: '/'
            };

            mockDetector.detectApplicationType.mockReturnValue(detection);
            mockDetector.getBaseUrl.mockReturnValue('http://localhost:8080');

            // Mock successful startup
            const startPromise = processManager.startApplication(projectPath);

            // Simulate application ready event
            setTimeout(() => {
                const stdoutCallback = mockProcess.stdout.on.mock.calls
                    .find(call => call[0] === 'data')[1];
                stdoutCallback('Started Application in 5.123 seconds');
            }, 100);

            const processInfo = await startPromise;

            expect(processInfo).toMatchObject({
                projectPath,
                detection,
                status: 'running',
                baseUrl: 'http://localhost:8080'
            });

            expect(spawn).toHaveBeenCalledWith('mvn', ['spring-boot:run'], {
                cwd: projectPath,
                stdio: ['pipe', 'pipe', 'pipe'],
                env: expect.any(Object)
            });
        });

        test('should start Gradle application successfully', async () => {
            const projectPath = '/test/gradle-project';
            const detection = {
                type: 'gradle',
                startCommand: './gradlew bootRun',
                port: 8080,
                contextPath: '/'
            };

            mockDetector.detectApplicationType.mockReturnValue(detection);
            mockDetector.getBaseUrl.mockReturnValue('http://localhost:8080');

            const startPromise = processManager.startApplication(projectPath);

            // Simulate application ready event
            setTimeout(() => {
                const stdoutCallback = mockProcess.stdout.on.mock.calls
                    .find(call => call[0] === 'data')[1];
                stdoutCallback('Tomcat started on port 8080');
            }, 100);

            const processInfo = await startPromise;

            expect(processInfo.detection.type).toBe('gradle');
            expect(spawn).toHaveBeenCalledWith('./gradlew', ['bootRun'], {
                cwd: projectPath,
                stdio: ['pipe', 'pipe', 'pipe'],
                env: expect.any(Object)
            });
        });

        test('should reject for unsupported application type', async () => {
            const projectPath = '/test/unknown-project';
            const detection = {
                type: 'unknown',
                startCommand: null
            };

            mockDetector.detectApplicationType.mockReturnValue(detection);

            await expect(processManager.startApplication(projectPath))
                .rejects.toThrow('Unsupported application type in: /test/unknown-project');
        });

        test('should handle process startup timeout', async () => {
            const projectPath = '/test/timeout-project';
            const detection = {
                type: 'maven',
                startCommand: 'mvn spring-boot:run',
                port: 8080,
                contextPath: '/'
            };

            mockDetector.detectApplicationType.mockReturnValue(detection);
            mockDetector.getBaseUrl.mockReturnValue('http://localhost:8080');

            // Don't simulate ready event - should timeout
            await expect(processManager.startApplication(projectPath, { timeout: 1000 }))
                .rejects.toThrow('Application start timeout after 1000ms');
        });

        test('should handle process error', async () => {
            const projectPath = '/test/error-project';
            const detection = {
                type: 'maven',
                startCommand: 'mvn spring-boot:run',
                port: 8080,
                contextPath: '/'
            };

            mockDetector.detectApplicationType.mockReturnValue(detection);
            mockDetector.getBaseUrl.mockReturnValue('http://localhost:8080');

            const startPromise = processManager.startApplication(projectPath);

            // Simulate process error
            setTimeout(() => {
                const errorCallback = mockProcess.on.mock.calls
                    .find(call => call[0] === 'error')[1];
                errorCallback(new Error('Process failed'));
            }, 100);

            await expect(startPromise).rejects.toThrow('Process error');
        });
    });

    describe('stopApplication', () => {
        test('should stop application successfully', async () => {
            const projectPath = '/test/project';
            const detection = {
                type: 'maven',
                startCommand: 'mvn spring-boot:run',
                port: 8080,
                contextPath: '/'
            };

            mockDetector.detectApplicationType.mockReturnValue(detection);
            mockDetector.getBaseUrl.mockReturnValue('http://localhost:8080');

            // Start application first
            const startPromise = processManager.startApplication(projectPath);
            
            setTimeout(() => {
                const stdoutCallback = mockProcess.stdout.on.mock.calls
                    .find(call => call[0] === 'data')[1];
                stdoutCallback('Started Application in 5.123 seconds');
            }, 50);

            const processInfo = await startPromise;

            // Now stop it
            const stopPromise = processManager.stopApplication(processInfo.id);

            // Simulate process exit
            setTimeout(() => {
                const exitCallback = mockProcess.on.mock.calls
                    .find(call => call[0] === 'exit')[1];
                exitCallback(0, null);
            }, 50);

            const result = await stopPromise;

            expect(result).toBe(true);
            expect(mockProcess.kill).toHaveBeenCalledWith('SIGTERM');
        });

        test('should throw error for non-existent process', async () => {
            await expect(processManager.stopApplication('non-existent'))
                .rejects.toThrow('Process not found');
        });
    });

    describe('getRunningProcesses', () => {
        test('should return list of running processes', async () => {
            const projectPath = '/test/project';
            const detection = {
                type: 'maven',
                startCommand: 'mvn spring-boot:run',
                port: 8080,
                contextPath: '/'
            };

            mockDetector.detectApplicationType.mockReturnValue(detection);
            mockDetector.getBaseUrl.mockReturnValue('http://localhost:8080');

            // Start application
            const startPromise = processManager.startApplication(projectPath);
            
            setTimeout(() => {
                const stdoutCallback = mockProcess.stdout.on.mock.calls
                    .find(call => call[0] === 'data')[1];
                stdoutCallback('Started Application in 5.123 seconds');
            }, 50);

            await startPromise;

            const runningProcesses = processManager.getRunningProcesses();

            expect(runningProcesses).toHaveLength(1);
            expect(runningProcesses[0]).toMatchObject({
                projectPath,
                type: 'maven',
                status: 'running',
                baseUrl: 'http://localhost:8080',
                port: 8080
            });
        });

        test('should return empty array when no processes running', () => {
            const runningProcesses = processManager.getRunningProcesses();
            expect(runningProcesses).toHaveLength(0);
        });
    });

    describe('_isApplicationReady', () => {
        test('should detect Spring Boot startup completion', () => {
            const logs = [
                'Starting Application...',
                'Started Application in 5.123 seconds',
                'Application is ready'
            ];

            logs.forEach(log => {
                const isReady = processManager._isApplicationReady(log);
                if (log.includes('Started') && log.includes('seconds')) {
                    expect(isReady).toBe(true);
                }
            });
        });

        test('should detect Tomcat startup completion', () => {
            const log = 'Tomcat started on port 8080';
            const isReady = processManager._isApplicationReady(log);
            expect(isReady).toBe(true);
        });

        test('should not detect incomplete startup', () => {
            const logs = [
                'Starting application...',
                'Loading configuration...',
                'Initializing beans...'
            ];

            logs.forEach(log => {
                const isReady = processManager._isApplicationReady(log);
                expect(isReady).toBe(false);
            });
        });
    });
});
