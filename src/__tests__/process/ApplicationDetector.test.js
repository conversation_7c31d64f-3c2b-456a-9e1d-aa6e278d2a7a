const fs = require('fs');
const path = require('path');
const ApplicationDetector = require('../../process/ApplicationDetector');

// Mock fs module
jest.mock('fs');

describe('ApplicationDetector', () => {
    let detector;

    beforeEach(() => {
        detector = new ApplicationDetector();
        jest.clearAllMocks();
    });

    describe('detectApplicationType', () => {
        test('should detect Maven project', () => {
            const projectPath = '/test/maven-project';
            const pomPath = path.join(projectPath, 'pom.xml');
            
            fs.existsSync.mockImplementation((filePath) => {
                return filePath === projectPath || filePath === pomPath;
            });

            fs.readFileSync.mockReturnValue(`
                <project>
                    <groupId>com.example</groupId>
                    <artifactId>test-app</artifactId>
                    <dependencies>
                        <dependency>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-starter-web</artifactId>
                        </dependency>
                    </dependencies>
                    <build>
                        <plugins>
                            <plugin>
                                <groupId>org.springframework.boot</groupId>
                                <artifactId>spring-boot-maven-plugin</artifactId>
                            </plugin>
                        </plugins>
                    </build>
                </project>
            `);

            const result = detector.detectApplicationType(projectPath);

            expect(result.type).toBe('maven');
            expect(result.buildFile).toBe(pomPath);
            expect(result.startCommand).toBe('mvn spring-boot:run');
            expect(result.port).toBe(8080);
            expect(result.contextPath).toBe('/');
        });

        test('should detect Gradle project', () => {
            const projectPath = '/test/gradle-project';
            const buildGradlePath = path.join(projectPath, 'build.gradle');
            
            fs.existsSync.mockImplementation((filePath) => {
                if (filePath === projectPath) return true;
                if (filePath === buildGradlePath) return true;
                if (filePath === path.join(projectPath, 'pom.xml')) return false;
                if (filePath === path.join(projectPath, 'build.gradle.kts')) return false;
                if (filePath === path.join(projectPath, 'gradlew')) return true; // 模拟gradlew存在
                if (filePath === path.join(projectPath, 'gradlew.bat')) return false;
                return false;
            });

            fs.readFileSync.mockReturnValue(`
                plugins {
                    id 'org.springframework.boot' version '2.7.0'
                    id 'java'
                }
                
                dependencies {
                    implementation 'org.springframework.boot:spring-boot-starter-web'
                }
            `);

            const result = detector.detectApplicationType(projectPath);

            expect(result.type).toBe('gradle');
            expect(result.buildFile).toBe(buildGradlePath);
            expect(result.startCommand).toBe('./gradlew bootRun');
            expect(result.port).toBe(8080);
        });

        test('should return unknown for unsupported project', () => {
            const projectPath = '/test/unknown-project';
            
            fs.existsSync.mockImplementation((filePath) => {
                if (filePath === projectPath) return true;
                return false; // No pom.xml or build.gradle
            });

            const result = detector.detectApplicationType(projectPath);

            expect(result.type).toBe('unknown');
            expect(result.buildFile).toBeNull();
            expect(result.startCommand).toBeNull();
        });

        test('should throw error for non-existent project path', () => {
            const projectPath = '/non/existent/path';
            
            fs.existsSync.mockReturnValue(false);

            expect(() => {
                detector.detectApplicationType(projectPath);
            }).toThrow('Project path does not exist');
        });
    });

    describe('_extractPortFromMaven', () => {
        test('should extract port from server.port property', () => {
            const pomPath = '/test/pom.xml';
            
            fs.readFileSync.mockReturnValue(`
                <project>
                    <properties>
                        <server.port>9090</server.port>
                    </properties>
                </project>
            `);

            const port = detector._extractPortFromMaven(pomPath);
            expect(port).toBe(9090);
        });

        test('should extract port from plugin configuration', () => {
            const pomPath = '/test/pom.xml';
            
            fs.readFileSync.mockReturnValue(`
                <project>
                    <build>
                        <plugins>
                            <plugin>
                                <configuration>
                                    <port>8081</port>
                                </configuration>
                            </plugin>
                        </plugins>
                    </build>
                </project>
            `);

            const port = detector._extractPortFromMaven(pomPath);
            expect(port).toBe(8081);
        });

        test('should return default port when not specified', () => {
            const pomPath = '/test/pom.xml';
            
            fs.readFileSync.mockReturnValue(`
                <project>
                    <groupId>com.example</groupId>
                    <artifactId>test-app</artifactId>
                </project>
            `);

            const port = detector._extractPortFromMaven(pomPath);
            expect(port).toBe(8080);
        });
    });

    describe('isSpringBootProject', () => {
        test('should identify Spring Boot Maven project', () => {
            const projectPath = '/test/spring-boot-project';
            const pomPath = path.join(projectPath, 'pom.xml');
            
            fs.existsSync.mockImplementation((filePath) => {
                return filePath === projectPath || filePath === pomPath;
            });

            fs.readFileSync.mockReturnValue(`
                <project>
                    <dependencies>
                        <dependency>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-starter</artifactId>
                        </dependency>
                    </dependencies>
                </project>
            `);

            const isSpringBoot = detector.isSpringBootProject(projectPath);
            expect(isSpringBoot).toBe(true);
        });

        test('should identify Spring Boot Gradle project', () => {
            const projectPath = '/test/spring-boot-gradle-project';
            const buildGradlePath = path.join(projectPath, 'build.gradle');
            
            fs.existsSync.mockImplementation((filePath) => {
                if (filePath === projectPath) return true;
                if (filePath === buildGradlePath) return true;
                if (filePath === path.join(projectPath, 'pom.xml')) return false;
                return false;
            });

            fs.readFileSync.mockReturnValue(`
                plugins {
                    id 'org.springframework.boot' version '2.7.0'
                }
            `);

            const isSpringBoot = detector.isSpringBootProject(projectPath);
            expect(isSpringBoot).toBe(true);
        });

        test('should return false for non-Spring Boot project', () => {
            const projectPath = '/test/regular-project';
            const pomPath = path.join(projectPath, 'pom.xml');
            
            fs.existsSync.mockImplementation((filePath) => {
                return filePath === projectPath || filePath === pomPath;
            });

            fs.readFileSync.mockReturnValue(`
                <project>
                    <dependencies>
                        <dependency>
                            <groupId>junit</groupId>
                            <artifactId>junit</artifactId>
                        </dependency>
                    </dependencies>
                </project>
            `);

            const isSpringBoot = detector.isSpringBootProject(projectPath);
            expect(isSpringBoot).toBe(false);
        });
    });

    describe('getBaseUrl', () => {
        test('should generate correct base URL', () => {
            const detection = {
                port: 8080,
                contextPath: '/'
            };

            const baseUrl = detector.getBaseUrl(detection);
            expect(baseUrl).toBe('http://localhost:8080');
        });

        test('should include context path in URL', () => {
            const detection = {
                port: 9090,
                contextPath: '/myapp'
            };

            const baseUrl = detector.getBaseUrl(detection);
            expect(baseUrl).toBe('http://localhost:9090/myapp');
        });
    });
});
