const path = require('path');
const fs = require('fs').promises;
const RouteDiscovery = require('../../analysis/RouteDiscovery');

// 这是一个集成测试，需要实际的Java项目来运行
// 在CI环境中可能需要跳过这些测试
const shouldRunIntegrationTests = process.env.RUN_INTEGRATION_TESTS === 'true';

describe('Dynamic Analysis Integration', () => {
    let discovery;
    const fixturesPath = path.join(__dirname, '../../../fixtures/source');
    const outputDir = path.join(__dirname, '../../../output/test-dynamic-analysis');

    beforeAll(async () => {
        // 清理输出目录
        try {
            await fs.rmdir(outputDir, { recursive: true });
        } catch (error) {
            // 目录可能不存在，忽略错误
        }
    });

    beforeEach(() => {
        discovery = new RouteDiscovery({
            includeStaticResources: true,
            includeWebXml: true,
            includeAnnotations: true,
            includeDynamicAnalysis: false, // 在单独的测试中启用
            dynamicAnalysisTimeout: 60000,
            savePageContents: true,
            outputDir
        });
    });

    afterEach(async () => {
        if (discovery) {
            await discovery.cleanup();
        }
    });

    describe('Static Analysis Only', () => {
        test('should discover routes without dynamic analysis', async () => {
            const manifest = await discovery.discoverRoutes(fixturesPath);

            expect(manifest).toBeDefined();
            expect(manifest.routes).toBeInstanceOf(Array);
            expect(manifest.routes.length).toBeGreaterThan(0);

            // 应该没有动态分析的路由
            const dynamicRoutes = manifest.routes.filter(r => r.source === 'dynamic-analysis');
            expect(dynamicRoutes).toHaveLength(0);
        });

        test('should analyze for React Router without dynamic analysis', async () => {
            await discovery.discoverRoutes(fixturesPath);
            const analysis = discovery.analyzeForReactRouter();

            expect(analysis).toHaveProperty('dynamicRoutes');
            expect(analysis).toHaveProperty('staticRoutes');
            expect(analysis).toHaveProperty('nestedRoutes');
            expect(analysis).toHaveProperty('suggestions');
        });
    });

    describe('Application Detection', () => {
        test('should detect application type correctly', () => {
            const detector = discovery.processManager.detector;
            const detection = detector.detectApplicationType(fixturesPath);

            expect(detection.type).toMatch(/maven|gradle/);
            expect(detection.startCommand).toBeDefined();
            expect(detection.port).toBeGreaterThan(0);
            expect(detection.contextPath).toBeDefined();
        });

        test('should generate correct base URL', () => {
            const detector = discovery.processManager.detector;
            const detection = detector.detectApplicationType(fixturesPath);
            const baseUrl = detector.getBaseUrl(detection);

            expect(baseUrl).toMatch(/^http:\/\/localhost:\d+/);
        });
    });

    // 这些测试需要实际启动Java应用，只在特定环境下运行
    (shouldRunIntegrationTests ? describe : describe.skip)('Full Dynamic Analysis', () => {
        beforeEach(() => {
            discovery = new RouteDiscovery({
                includeStaticResources: true,
                includeWebXml: true,
                includeAnnotations: true,
                includeDynamicAnalysis: true,
                dynamicAnalysisTimeout: 120000,
                savePageContents: true,
                outputDir
            });
        });

        test('should perform complete dynamic analysis', async () => {
            const manifest = await discovery.discoverRoutes(fixturesPath);

            expect(manifest).toBeDefined();
            expect(manifest.routes).toBeInstanceOf(Array);
            expect(manifest.routes.length).toBeGreaterThan(0);

            // 应该包含动态分析的路由
            const dynamicRoutes = manifest.routes.filter(r => r.source === 'dynamic-analysis');
            expect(dynamicRoutes.length).toBeGreaterThan(0);

            // 检查动态路由的结构
            dynamicRoutes.forEach(route => {
                expect(route).toHaveProperty('path');
                expect(route).toHaveProperty('method', 'GET');
                expect(route).toHaveProperty('type', 'dynamic');
                expect(route).toHaveProperty('confidence');
                expect(route).toHaveProperty('metadata');
                expect(route.metadata).toHaveProperty('status');
                expect(route.metadata).toHaveProperty('discoveredAt');
            });
        }, 180000); // 3分钟超时

        test('should save page contents during dynamic analysis', async () => {
            await discovery.discoverRoutes(fixturesPath);

            // 检查输出目录是否存在
            const outputExists = await fs.access(outputDir).then(() => true).catch(() => false);
            expect(outputExists).toBe(true);

            // 检查是否有保存的文件
            const files = await fs.readdir(outputDir);
            const htmlFiles = files.filter(f => f.endsWith('.html'));
            const jsonFiles = files.filter(f => f.endsWith('.json'));

            expect(htmlFiles.length).toBeGreaterThan(0);
            expect(jsonFiles.length).toBeGreaterThan(0);
        }, 180000);

        test('should handle application startup and shutdown correctly', async () => {
            // 手动启动应用
            const appInfo = await discovery.startApplicationForAnalysis(fixturesPath);
            
            expect(appInfo).toBeDefined();
            expect(appInfo.id).toBeDefined();
            expect(appInfo.pid).toBeGreaterThan(0);
            expect(appInfo.baseUrl).toMatch(/^http:\/\/localhost:\d+/);
            expect(appInfo.status).toBe('running');

            // 检查应用是否在运行
            const runningApp = discovery.getRunningApplication();
            expect(runningApp).toBeDefined();
            expect(runningApp.id).toBe(appInfo.id);

            // 停止应用
            await discovery.stopApplication();
            
            const stoppedApp = discovery.getRunningApplication();
            expect(stoppedApp).toBeNull();
        }, 180000);
    });

    describe('Error Handling', () => {
        test('should handle non-existent project path gracefully', async () => {
            const nonExistentPath = '/non/existent/path';
            
            await expect(discovery.discoverRoutes(nonExistentPath))
                .rejects.toThrow();
        });

        test('should handle application startup failure gracefully', async () => {
            // 使用一个无效的项目路径
            const invalidPath = __dirname; // 这个目录不包含Java项目
            
            discovery.options.includeDynamicAnalysis = true;
            
            // 应该完成静态分析，但动态分析会失败（被捕获）
            const manifest = await discovery.discoverRoutes(invalidPath);
            
            expect(manifest).toBeDefined();
            // 不应该有动态路由，因为应用启动失败
            const dynamicRoutes = manifest.routes.filter(r => r.source === 'dynamic-analysis');
            expect(dynamicRoutes).toHaveLength(0);
        });
    });

    describe('Process Management', () => {
        test('should track running processes correctly', async () => {
            const processManager = discovery.processManager;
            
            // 初始状态应该没有运行的进程
            expect(processManager.getRunningProcesses()).toHaveLength(0);
            
            // 这里我们不实际启动进程，只测试数据结构
            expect(processManager.getProcessInfo('non-existent')).toBeNull();
        });

        test('should handle cleanup correctly', async () => {
            // 测试清理功能不会抛出错误
            await expect(discovery.cleanup()).resolves.not.toThrow();
        });
    });

    describe('Page Analyzer', () => {
        test('should initialize and close browser correctly', async () => {
            const analyzer = discovery.pageAnalyzer;
            
            // 初始化浏览器
            await analyzer.initialize();
            expect(analyzer.browser).toBeDefined();
            
            // 关闭浏览器
            await analyzer.close();
            expect(analyzer.browser).toBeNull();
        });

        test('should generate correct file names', () => {
            const analyzer = discovery.pageAnalyzer;
            
            const testCases = [
                { url: 'http://localhost:8080/', expected: 'index' },
                { url: 'http://localhost:8080/users', expected: 'users' },
                { url: 'http://localhost:8080/api/users/123', expected: 'api_users_123' },
                { url: 'http://localhost:8080/search?q=test&page=1', expected: 'search_q_test_page_1' }
            ];

            testCases.forEach(({ url, expected }) => {
                const urlObj = new URL(url);
                const fileName = analyzer._generateFileName(urlObj);
                expect(fileName).toBe(expected);
            });
        });
    });
});
