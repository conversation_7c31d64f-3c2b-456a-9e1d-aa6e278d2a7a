const fs = require('fs');
const path = require('path');
const WebXmlParser = require('../parsers/WebXmlParser');

describe('WebXmlParser', () => {
  let parser;
  let tempDir;

  beforeEach(() => {
    parser = new WebXmlParser();
    tempDir = path.join(__dirname, 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
  });

  afterEach(() => {
    // Clean up temp files
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });

  describe('parseWebXml', () => {
    test('should parse basic web.xml with servlet mappings', async () => {
      const webXmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE web-app PUBLIC "-//Sun Microsystems, Inc.//DTD Web Application 2.3//EN" "http://java.sun.com/dtd/web-app_2_3.dtd">
<web-app>
  <servlet>
    <servlet-name>BlogController</servlet-name>
    <servlet-class>com.example.BlogController</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>BlogController</servlet-name>
    <url-pattern>/posts/*</url-pattern>
  </servlet-mapping>
</web-app>`;

      const webXmlPath = path.join(tempDir, 'web.xml');
      fs.writeFileSync(webXmlPath, webXmlContent);

      const mappings = await parser.parseWebXml(webXmlPath);

      expect(mappings).toHaveLength(1);
      expect(mappings[0]).toEqual({
        servletName: 'BlogController',
        urlPatterns: ['/posts/*'],
        servletClass: 'com.example.BlogController'
      });
    });

    test('should handle multiple URL patterns for single servlet', async () => {
      const webXmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<web-app>
  <servlet>
    <servlet-name>ApiController</servlet-name>
    <servlet-class>com.example.ApiController</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>ApiController</servlet-name>
    <url-pattern>/api/*</url-pattern>
    <url-pattern>/rest/*</url-pattern>
  </servlet-mapping>
</web-app>`;

      const webXmlPath = path.join(tempDir, 'web.xml');
      fs.writeFileSync(webXmlPath, webXmlContent);

      const mappings = await parser.parseWebXml(webXmlPath);

      expect(mappings).toHaveLength(1);
      expect(mappings[0].urlPatterns).toEqual(['/api/*', '/rest/*']);
    });

    test('should handle missing servlet class', async () => {
      const webXmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<web-app>
  <servlet-mapping>
    <servlet-name>UnknownServlet</servlet-name>
    <url-pattern>/unknown/*</url-pattern>
  </servlet-mapping>
</web-app>`;

      const webXmlPath = path.join(tempDir, 'web.xml');
      fs.writeFileSync(webXmlPath, webXmlContent);

      const mappings = await parser.parseWebXml(webXmlPath);

      expect(mappings).toHaveLength(1);
      expect(mappings[0].servletClass).toBe('unknown');
    });

    test('should throw error for non-existent file', async () => {
      await expect(parser.parseWebXml('/non/existent/web.xml'))
        .rejects.toThrow('web.xml not found at');
    });

    test('should handle empty web.xml', async () => {
      const webXmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<web-app>
</web-app>`;

      const webXmlPath = path.join(tempDir, 'web.xml');
      fs.writeFileSync(webXmlPath, webXmlContent);

      const mappings = await parser.parseWebXml(webXmlPath);
      expect(mappings).toHaveLength(0);
    });
  });

  describe('convertToRoutes', () => {
    test('should convert servlet mappings to routes', () => {
      const mappings = [
        {
          servletName: 'BlogController',
          urlPatterns: ['/posts/*', '/blog'],
          servletClass: 'com.example.BlogController'
        }
      ];

      const routes = parser.convertToRoutes(mappings);

      expect(routes).toHaveLength(2);
      expect(routes[0]).toEqual({
        path: '/posts/**',
        method: 'ALL',
        type: 'servlet',
        source: 'web.xml',
        confidence: 0.9,
        metadata: {
          servletName: 'BlogController',
          servletClass: 'com.example.BlogController',
          originalPattern: '/posts/*'
        }
      });
      expect(routes[1]).toEqual({
        path: '/blog',
        method: 'ALL',
        type: 'servlet',
        source: 'web.xml',
        confidence: 0.9,
        metadata: {
          servletName: 'BlogController',
          servletClass: 'com.example.BlogController',
          originalPattern: '/blog'
        }
      });
    });
  });

  describe('normalizeUrlPattern', () => {
    test('should normalize various URL patterns', () => {
      expect(parser.normalizeUrlPattern('/posts/*')).toBe('/posts/**');
      expect(parser.normalizeUrlPattern('posts/*')).toBe('/posts/**');
      expect(parser.normalizeUrlPattern('/api')).toBe('/api');
      expect(parser.normalizeUrlPattern('api')).toBe('/api');
      expect(parser.normalizeUrlPattern('')).toBe('/');
      expect(parser.normalizeUrlPattern('/')).toBe('/');
    });
  });

  describe('normalizeArray', () => {
    test('should normalize single items and arrays', () => {
      expect(parser.normalizeArray('single')).toEqual(['single']);
      expect(parser.normalizeArray(['array', 'items'])).toEqual(['array', 'items']);
      expect(parser.normalizeArray(null)).toEqual([]);
      expect(parser.normalizeArray(undefined)).toEqual([]);
    });
  });
});
