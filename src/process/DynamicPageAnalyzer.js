const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

/**
 * 动态页面分析器
 * 使用Puppeteer访问运行中的应用并下载HTML内容
 */
class DynamicPageAnalyzer {
    constructor(options = {}) {
        this.options = {
            headless: true,
            timeout: 30000,
            waitForSelector: 'body',
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ...options
        };
        this.browser = null;
        this.pages = new Map(); // 存储页面实例
    }

    /**
     * 初始化浏览器
     * @returns {Promise<void>}
     */
    async initialize() {
        if (this.browser) {
            return;
        }

        console.log('Launching browser...');
        this.browser = await puppeteer.launch({
            headless: this.options.headless,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ]
        });
        console.log('Browser launched successfully');
    }

    /**
     * 关闭浏览器
     * @returns {Promise<void>}
     */
    async close() {
        if (this.browser) {
            await this.browser.close();
            this.browser = null;
            this.pages.clear();
            console.log('Browser closed');
        }
    }

    /**
     * 访问并分析单个页面
     * @param {string} url - 页面URL
     * @param {Object} options - 访问选项
     * @returns {Promise<Object>} 页面分析结果
     */
    async analyzePage(url, options = {}) {
        await this.initialize();

        const pageOptions = { ...this.options, ...options };
        const page = await this.browser.newPage();

        try {
            // 设置用户代理
            await page.setUserAgent(pageOptions.userAgent);

            // 设置视口
            await page.setViewport({ width: 1920, height: 1080 });

            console.log(`Navigating to: ${url}`);
            
            // 导航到页面
            const response = await page.goto(url, {
                waitUntil: 'networkidle2',
                timeout: pageOptions.timeout
            });

            if (!response.ok()) {
                throw new Error(`HTTP ${response.status()}: ${response.statusText()}`);
            }

            // 等待页面加载完成
            if (pageOptions.waitForSelector) {
                await page.waitForSelector(pageOptions.waitForSelector, {
                    timeout: pageOptions.timeout
                });
            }

            // 等待额外的加载时间（处理动态内容）
            await page.waitForTimeout(2000);

            // 获取页面内容
            const content = await page.content();
            
            // 获取页面信息
            const pageInfo = await page.evaluate(() => ({
                title: document.title,
                url: window.location.href,
                forms: Array.from(document.forms).map(form => ({
                    action: form.action,
                    method: form.method,
                    elements: Array.from(form.elements).map(el => ({
                        name: el.name,
                        type: el.type,
                        id: el.id
                    }))
                })),
                links: Array.from(document.links).map(link => ({
                    href: link.href,
                    text: link.textContent.trim()
                })),
                scripts: Array.from(document.scripts).map(script => ({
                    src: script.src,
                    type: script.type
                })),
                stylesheets: Array.from(document.styleSheets).map(sheet => ({
                    href: sheet.href
                }))
            }));

            // 截图（可选）
            let screenshot = null;
            if (pageOptions.screenshot) {
                screenshot = await page.screenshot({
                    fullPage: true,
                    type: 'png'
                });
            }

            const result = {
                url,
                status: response.status(),
                statusText: response.statusText(),
                content,
                pageInfo,
                screenshot,
                timestamp: new Date(),
                loadTime: Date.now() - response.timing().requestTime
            };

            console.log(`Page analyzed successfully: ${url}`);
            return result;

        } catch (error) {
            console.error(`Failed to analyze page ${url}:`, error.message);
            throw error;
        } finally {
            await page.close();
        }
    }

    /**
     * 批量分析多个页面
     * @param {Array<string>} urls - URL列表
     * @param {Object} options - 分析选项
     * @returns {Promise<Array<Object>>} 分析结果列表
     */
    async analyzePages(urls, options = {}) {
        const results = [];
        const batchOptions = {
            concurrent: 3, // 并发数量
            delay: 1000, // 请求间隔
            ...options
        };

        console.log(`Starting batch analysis of ${urls.length} pages...`);

        // 分批处理URL
        for (let i = 0; i < urls.length; i += batchOptions.concurrent) {
            const batch = urls.slice(i, i + batchOptions.concurrent);
            
            const batchPromises = batch.map(async (url) => {
                try {
                    const result = await this.analyzePage(url, options);
                    return { success: true, url, result };
                } catch (error) {
                    console.error(`Failed to analyze ${url}:`, error.message);
                    return { success: false, url, error: error.message };
                }
            });

            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);

            // 添加延迟避免过于频繁的请求
            if (i + batchOptions.concurrent < urls.length && batchOptions.delay > 0) {
                await new Promise(resolve => setTimeout(resolve, batchOptions.delay));
            }
        }

        console.log(`Batch analysis completed. Success: ${results.filter(r => r.success).length}, Failed: ${results.filter(r => !r.success).length}`);
        return results;
    }

    /**
     * 保存页面内容到文件
     * @param {Object} pageResult - 页面分析结果
     * @param {string} outputDir - 输出目录
     * @returns {Promise<string>} 保存的文件路径
     */
    async savePageContent(pageResult, outputDir) {
        await fs.mkdir(outputDir, { recursive: true });

        // 生成文件名
        const urlObj = new URL(pageResult.url);
        const fileName = this._generateFileName(urlObj);
        const filePath = path.join(outputDir, `${fileName}.html`);

        // 保存HTML内容
        await fs.writeFile(filePath, pageResult.content, 'utf8');

        // 保存页面信息
        const infoPath = path.join(outputDir, `${fileName}.json`);
        await fs.writeFile(infoPath, JSON.stringify({
            url: pageResult.url,
            status: pageResult.status,
            statusText: pageResult.statusText,
            pageInfo: pageResult.pageInfo,
            timestamp: pageResult.timestamp,
            loadTime: pageResult.loadTime
        }, null, 2), 'utf8');

        // 保存截图（如果有）
        if (pageResult.screenshot) {
            const screenshotPath = path.join(outputDir, `${fileName}.png`);
            await fs.writeFile(screenshotPath, pageResult.screenshot);
        }

        console.log(`Page content saved: ${filePath}`);
        return filePath;
    }

    /**
     * 批量保存页面内容
     * @param {Array<Object>} pageResults - 页面分析结果列表
     * @param {string} outputDir - 输出目录
     * @returns {Promise<Array<string>>} 保存的文件路径列表
     */
    async saveAllPageContents(pageResults, outputDir) {
        const savedFiles = [];

        for (const result of pageResults) {
            if (result.success) {
                try {
                    const filePath = await this.savePageContent(result.result, outputDir);
                    savedFiles.push(filePath);
                } catch (error) {
                    console.error(`Failed to save page content for ${result.url}:`, error.message);
                }
            }
        }

        console.log(`Saved ${savedFiles.length} page contents to ${outputDir}`);
        return savedFiles;
    }

    /**
     * 生成文件名
     * @param {URL} urlObj - URL对象
     * @returns {string} 文件名
     */
    _generateFileName(urlObj) {
        let fileName = urlObj.pathname.replace(/\//g, '_');
        
        // 处理根路径
        if (fileName === '_' || fileName === '') {
            fileName = 'index';
        }
        
        // 移除文件扩展名
        fileName = fileName.replace(/\.[^.]*$/, '');
        
        // 添加查询参数（如果有）
        if (urlObj.search) {
            const params = urlObj.search.substring(1).replace(/[&=]/g, '_');
            fileName += `_${params}`;
        }
        
        // 清理文件名
        fileName = fileName.replace(/[^a-zA-Z0-9_-]/g, '_');
        fileName = fileName.replace(/_+/g, '_');
        fileName = fileName.replace(/^_|_$/g, '');
        
        return fileName || 'page';
    }

    /**
     * 检查应用是否可访问
     * @param {string} baseUrl - 应用基础URL
     * @param {number} timeout - 超时时间
     * @returns {Promise<boolean>} 是否可访问
     */
    async isApplicationAccessible(baseUrl, timeout = 10000) {
        await this.initialize();

        const page = await this.browser.newPage();
        
        try {
            const response = await page.goto(baseUrl, {
                waitUntil: 'networkidle2',
                timeout
            });
            
            return response.ok();
        } catch (error) {
            return false;
        } finally {
            await page.close();
        }
    }
}

module.exports = DynamicPageAnalyzer;
