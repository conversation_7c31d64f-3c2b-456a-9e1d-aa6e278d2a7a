const { spawn } = require('cross-spawn');
const EventEmitter = require('events');
const ApplicationDetector = require('./ApplicationDetector');

/**
 * 进程管理器
 * 负责启动、监控和停止Java应用进程
 */
class ProcessManager extends EventEmitter {
    constructor() {
        super();
        this.detector = new ApplicationDetector();
        this.runningProcesses = new Map(); // 存储运行中的进程
        this.processCounter = 0;
    }

    /**
     * 启动Java应用
     * @param {string} projectPath - 项目路径
     * @param {Object} options - 启动选项
     * @returns {Promise<Object>} 进程信息
     */
    async startApplication(projectPath, options = {}) {
        try {
            // 检测应用类型
            const detection = this.detector.detectApplicationType(projectPath);
            
            if (detection.type === this.detector.supportedTypes.UNKNOWN) {
                throw new Error(`Unsupported application type in: ${projectPath}`);
            }

            console.log(`Detected ${detection.type} application at: ${projectPath}`);
            console.log(`Start command: ${detection.startCommand}`);
            console.log(`Expected port: ${detection.port}`);

            // 生成进程ID
            const processId = `proc_${++this.processCounter}`;
            
            // 解析启动命令
            const [command, ...args] = detection.startCommand.split(' ');
            
            // 启动进程
            const childProcess = spawn(command, args, {
                cwd: projectPath,
                stdio: ['pipe', 'pipe', 'pipe'],
                env: {
                    ...process.env,
                    ...options.env
                }
            });

            // 创建进程信息对象
            const processInfo = {
                id: processId,
                pid: childProcess.pid,
                projectPath,
                detection,
                process: childProcess,
                startTime: new Date(),
                status: 'starting',
                logs: [],
                baseUrl: this.detector.getBaseUrl(detection)
            };

            // 存储进程信息
            this.runningProcesses.set(processId, processInfo);

            // 设置进程事件监听
            this._setupProcessListeners(processInfo);

            // 等待应用启动
            await this._waitForApplicationStart(processInfo, options.timeout || 120000);

            console.log(`Application started successfully: ${processInfo.baseUrl}`);
            
            return processInfo;

        } catch (error) {
            console.error('Failed to start application:', error.message);
            throw error;
        }
    }

    /**
     * 停止应用进程
     * @param {string} processId - 进程ID
     * @returns {Promise<boolean>} 是否成功停止
     */
    async stopApplication(processId) {
        const processInfo = this.runningProcesses.get(processId);
        
        if (!processInfo) {
            throw new Error(`Process not found: ${processId}`);
        }

        try {
            console.log(`Stopping application: ${processId}`);
            
            // 发送终止信号
            processInfo.process.kill('SIGTERM');
            
            // 等待进程结束
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    // 强制杀死进程
                    processInfo.process.kill('SIGKILL');
                    reject(new Error('Process termination timeout'));
                }, 10000);

                processInfo.process.on('exit', () => {
                    clearTimeout(timeout);
                    resolve();
                });
            });

            // 清理进程信息
            this.runningProcesses.delete(processId);
            
            console.log(`Application stopped: ${processId}`);
            return true;

        } catch (error) {
            console.error(`Failed to stop application ${processId}:`, error.message);
            return false;
        }
    }

    /**
     * 获取运行中的进程列表
     * @returns {Array} 进程信息列表
     */
    getRunningProcesses() {
        return Array.from(this.runningProcesses.values()).map(info => ({
            id: info.id,
            pid: info.pid,
            projectPath: info.projectPath,
            type: info.detection.type,
            status: info.status,
            startTime: info.startTime,
            baseUrl: info.baseUrl,
            port: info.detection.port
        }));
    }

    /**
     * 获取特定进程信息
     * @param {string} processId - 进程ID
     * @returns {Object|null} 进程信息
     */
    getProcessInfo(processId) {
        return this.runningProcesses.get(processId) || null;
    }

    /**
     * 停止所有运行中的进程
     * @returns {Promise<void>}
     */
    async stopAllProcesses() {
        const processIds = Array.from(this.runningProcesses.keys());
        
        console.log(`Stopping ${processIds.length} running processes...`);
        
        const stopPromises = processIds.map(id => 
            this.stopApplication(id).catch(error => 
                console.error(`Failed to stop process ${id}:`, error.message)
            )
        );

        await Promise.all(stopPromises);
        console.log('All processes stopped');
    }

    /**
     * 设置进程事件监听器
     * @param {Object} processInfo - 进程信息
     */
    _setupProcessListeners(processInfo) {
        const { process: childProcess, id } = processInfo;

        // 监听标准输出
        childProcess.stdout.on('data', (data) => {
            const log = data.toString();
            processInfo.logs.push({ type: 'stdout', message: log, timestamp: new Date() });
            
            // 检测启动完成信号
            if (this._isApplicationReady(log)) {
                processInfo.status = 'running';
                this.emit('applicationReady', processInfo);
            }
            
            this.emit('processLog', { processId: id, type: 'stdout', message: log });
        });

        // 监听标准错误
        childProcess.stderr.on('data', (data) => {
            const log = data.toString();
            processInfo.logs.push({ type: 'stderr', message: log, timestamp: new Date() });
            this.emit('processLog', { processId: id, type: 'stderr', message: log });
        });

        // 监听进程退出
        childProcess.on('exit', (code, signal) => {
            processInfo.status = 'stopped';
            processInfo.exitCode = code;
            processInfo.exitSignal = signal;
            
            console.log(`Process ${id} exited with code ${code}, signal ${signal}`);
            this.emit('processExit', { processId: id, code, signal });
        });

        // 监听进程错误
        childProcess.on('error', (error) => {
            processInfo.status = 'error';
            processInfo.error = error;
            
            console.error(`Process ${id} error:`, error.message);
            this.emit('processError', { processId: id, error });
        });
    }

    /**
     * 等待应用启动完成
     * @param {Object} processInfo - 进程信息
     * @param {number} timeout - 超时时间（毫秒）
     * @returns {Promise<void>}
     */
    async _waitForApplicationStart(processInfo, timeout = 120000) {
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error(`Application start timeout after ${timeout}ms`));
            }, timeout);

            // 监听应用就绪事件
            const onReady = (info) => {
                if (info.id === processInfo.id) {
                    clearTimeout(timeoutId);
                    this.removeListener('applicationReady', onReady);
                    this.removeListener('processError', onError);
                    this.removeListener('processExit', onExit);
                    resolve();
                }
            };

            // 监听进程错误
            const onError = (info) => {
                if (info.processId === processInfo.id) {
                    clearTimeout(timeoutId);
                    this.removeListener('applicationReady', onReady);
                    this.removeListener('processError', onError);
                    this.removeListener('processExit', onExit);
                    reject(new Error(`Process error: ${info.error.message}`));
                }
            };

            // 监听进程退出
            const onExit = (info) => {
                if (info.processId === processInfo.id) {
                    clearTimeout(timeoutId);
                    this.removeListener('applicationReady', onReady);
                    this.removeListener('processError', onError);
                    this.removeListener('processExit', onExit);
                    reject(new Error(`Process exited with code ${info.code}`));
                }
            };

            this.on('applicationReady', onReady);
            this.on('processError', onError);
            this.on('processExit', onExit);
        });
    }

    /**
     * 检测应用是否已就绪
     * @param {string} log - 日志内容
     * @returns {boolean} 是否就绪
     */
    _isApplicationReady(log) {
        // Spring Boot应用启动完成的标志
        const springBootReadyPatterns = [
            /Started .* in \d+\.\d+ seconds/,
            /Tomcat started on port/,
            /Jetty started on port/,
            /Application startup complete/
        ];

        return springBootReadyPatterns.some(pattern => pattern.test(log));
    }
}

module.exports = ProcessManager;
