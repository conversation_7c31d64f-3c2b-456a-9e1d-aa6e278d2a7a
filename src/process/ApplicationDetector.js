const fs = require('fs');
const path = require('path');

/**
 * 应用类型检测器
 * 用于识别Java项目的构建工具类型（Maven或Gradle）
 */
class ApplicationDetector {
    constructor() {
        this.supportedTypes = {
            MAVEN: 'maven',
            GRADLE: 'gradle',
            UNKNOWN: 'unknown'
        };
    }

    /**
     * 检测应用类型
     * @param {string} projectPath - 项目路径
     * @returns {Object} 检测结果
     */
    detectApplicationType(projectPath) {
        if (!fs.existsSync(projectPath)) {
            throw new Error(`Project path does not exist: ${projectPath}`);
        }

        const result = {
            type: this.supportedTypes.UNKNOWN,
            buildFile: null,
            startCommand: null,
            port: 8080, // 默认端口
            contextPath: '/'
        };

        // 检测Maven项目
        const pomPath = path.join(projectPath, 'pom.xml');
        if (fs.existsSync(pomPath)) {
            result.type = this.supportedTypes.MAVEN;
            result.buildFile = pomPath;
            result.startCommand = this._getMavenStartCommand(pomPath);
            result.port = this._extractPortFromMaven(pomPath);
            result.contextPath = this._extractContextPathFromMaven(pomPath);
            return result;
        }

        // 检测Gradle项目
        const gradleBuildPath = path.join(projectPath, 'build.gradle');
        const gradleBuildKtsPath = path.join(projectPath, 'build.gradle.kts');
        
        if (fs.existsSync(gradleBuildPath) || fs.existsSync(gradleBuildKtsPath)) {
            result.type = this.supportedTypes.GRADLE;
            result.buildFile = fs.existsSync(gradleBuildPath) ? gradleBuildPath : gradleBuildKtsPath;
            result.startCommand = this._getGradleStartCommand(projectPath);
            result.port = this._extractPortFromGradle(result.buildFile);
            result.contextPath = this._extractContextPathFromGradle(result.buildFile);
            return result;
        }

        return result;
    }

    /**
     * 获取Maven启动命令
     * @param {string} pomPath - pom.xml文件路径
     * @returns {string} 启动命令
     */
    _getMavenStartCommand(pomPath) {
        try {
            const pomContent = fs.readFileSync(pomPath, 'utf8');
            
            // 检测Spring Boot项目
            if (pomContent.includes('spring-boot-starter') || pomContent.includes('spring-boot-maven-plugin')) {
                return 'mvn spring-boot:run';
            }
            
            // 检测Tomcat插件
            if (pomContent.includes('tomcat7-maven-plugin') || pomContent.includes('tomcat-maven-plugin')) {
                return 'mvn tomcat7:run';
            }
            
            // 检测Jetty插件
            if (pomContent.includes('jetty-maven-plugin')) {
                return 'mvn jetty:run';
            }
            
            // 默认使用exec插件
            return 'mvn exec:java';
        } catch (error) {
            console.warn('Failed to read pom.xml:', error.message);
            return 'mvn spring-boot:run'; // 默认命令
        }
    }

    /**
     * 获取Gradle启动命令
     * @param {string} projectPath - 项目路径
     * @returns {string} 启动命令
     */
    _getGradleStartCommand(projectPath) {
        // 检查是否有gradlew包装器
        const gradlewPath = path.join(projectPath, 'gradlew');
        const gradlewBatPath = path.join(projectPath, 'gradlew.bat');
        const hasWrapper = fs.existsSync(gradlewPath) || fs.existsSync(gradlewBatPath);
        const gradleCmd = hasWrapper ? './gradlew' : 'gradle';

        return `${gradleCmd} bootRun`;
    }

    /**
     * 从Maven配置中提取端口号
     * @param {string} pomPath - pom.xml文件路径
     * @returns {number} 端口号
     */
    _extractPortFromMaven(pomPath) {
        try {
            const pomContent = fs.readFileSync(pomPath, 'utf8');
            
            // 查找server.port配置
            const portMatch = pomContent.match(/<server\.port>(\d+)<\/server\.port>/);
            if (portMatch) {
                return parseInt(portMatch[1]);
            }
            
            // 查找Spring Boot插件配置中的端口
            const springBootPortMatch = pomContent.match(/<port>(\d+)<\/port>/);
            if (springBootPortMatch) {
                return parseInt(springBootPortMatch[1]);
            }
            
            return 8080; // 默认端口
        } catch (error) {
            console.warn('Failed to extract port from pom.xml:', error.message);
            return 8080;
        }
    }

    /**
     * 从Gradle配置中提取端口号
     * @param {string} buildFilePath - build.gradle文件路径
     * @returns {number} 端口号
     */
    _extractPortFromGradle(buildFilePath) {
        try {
            const buildContent = fs.readFileSync(buildFilePath, 'utf8');
            
            // 查找server.port配置
            const portMatch = buildContent.match(/server\.port\s*=\s*(\d+)/);
            if (portMatch) {
                return parseInt(portMatch[1]);
            }
            
            return 8080; // 默认端口
        } catch (error) {
            console.warn('Failed to extract port from build.gradle:', error.message);
            return 8080;
        }
    }

    /**
     * 从Maven配置中提取上下文路径
     * @param {string} pomPath - pom.xml文件路径
     * @returns {string} 上下文路径
     */
    _extractContextPathFromMaven(pomPath) {
        try {
            const pomContent = fs.readFileSync(pomPath, 'utf8');
            
            // 查找context path配置
            const contextMatch = pomContent.match(/<server\.servlet\.context-path>([^<]+)<\/server\.servlet\.context-path>/);
            if (contextMatch) {
                return contextMatch[1];
            }
            
            const contextPathMatch = pomContent.match(/<contextPath>([^<]+)<\/contextPath>/);
            if (contextPathMatch) {
                return contextPathMatch[1];
            }
            
            return '/'; // 默认根路径
        } catch (error) {
            console.warn('Failed to extract context path from pom.xml:', error.message);
            return '/';
        }
    }

    /**
     * 从Gradle配置中提取上下文路径
     * @param {string} buildFilePath - build.gradle文件路径
     * @returns {string} 上下文路径
     */
    _extractContextPathFromGradle(buildFilePath) {
        try {
            const buildContent = fs.readFileSync(buildFilePath, 'utf8');
            
            // 查找context path配置
            const contextMatch = buildContent.match(/server\.servlet\.context-path\s*=\s*['"]([^'"]+)['"]/);
            if (contextMatch) {
                return contextMatch[1];
            }
            
            return '/'; // 默认根路径
        } catch (error) {
            console.warn('Failed to extract context path from build.gradle:', error.message);
            return '/';
        }
    }

    /**
     * 检查项目是否为Spring Boot项目
     * @param {string} projectPath - 项目路径
     * @returns {boolean} 是否为Spring Boot项目
     */
    isSpringBootProject(projectPath) {
        const detection = this.detectApplicationType(projectPath);
        
        if (detection.type === this.supportedTypes.MAVEN) {
            const pomContent = fs.readFileSync(detection.buildFile, 'utf8');
            return pomContent.includes('spring-boot-starter');
        }
        
        if (detection.type === this.supportedTypes.GRADLE) {
            const buildContent = fs.readFileSync(detection.buildFile, 'utf8');
            return buildContent.includes('spring-boot') || buildContent.includes('org.springframework.boot');
        }
        
        return false;
    }

    /**
     * 获取应用的基础URL
     * @param {Object} detection - 检测结果
     * @returns {string} 基础URL
     */
    getBaseUrl(detection) {
        const protocol = 'http';
        const host = 'localhost';
        const port = detection.port;
        const contextPath = detection.contextPath === '/' ? '' : detection.contextPath;
        
        return `${protocol}://${host}:${port}${contextPath}`;
    }
}

module.exports = ApplicationDetector;
