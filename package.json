{"name": "jsp2react-agent", "version": "1.0.0", "description": "JSP to React migration AI agent CLI tool", "main": "src/index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:integration": "RUN_INTEGRATION_TESTS=true jest --testPathPattern=integration", "dev": "node src/index.js", "example:dynamic": "node examples/dynamic-analysis-example.js", "example:app-management": "node examples/dynamic-analysis-example.js --app-management", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix"}, "keywords": ["jsp", "react", "migration", "ai", "agent"], "author": "phodal", "license": "MIT", "devDependencies": {"eslint": "^8.0.0", "jest": "^29.5.0"}, "dependencies": {"@ai-sdk/openai": "^1.3.23", "ai": "^4.3.18", "cross-spawn": "^7.0.6", "dotenv": "^17.2.0", "fast-xml-parser": "^4.3.0", "glob": "^10.3.0", "puppeteer": "^24.12.1", "xml2js": "^0.6.2"}}