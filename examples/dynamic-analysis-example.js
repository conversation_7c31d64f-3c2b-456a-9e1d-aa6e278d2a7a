#!/usr/bin/env node

/**
 * 动态分析示例
 * 演示如何使用进程管理功能启动Java应用并进行动态路由发现
 */

const path = require('path');
const RouteDiscovery = require('../src/analysis/RouteDiscovery');

async function runDynamicAnalysis() {
    // 配置路由发现选项
    const options = {
        includeStaticResources: true,
        includeWebXml: true,
        includeAnnotations: true,
        includeDynamicAnalysis: true, // 启用动态分析
        dynamicAnalysisTimeout: 120000, // 2分钟超时
        savePageContents: true,
        outputDir: './output/dynamic-analysis'
    };

    const discovery = new RouteDiscovery(options);
    
    try {
        // 项目路径 - 使用fixtures中的示例项目
        const projectPath = path.join(__dirname, '../fixtures/source');
        
        console.log('='.repeat(60));
        console.log('JSP to React Agent - Dynamic Analysis Example');
        console.log('='.repeat(60));
        console.log(`Project Path: ${projectPath}`);
        console.log(`Options:`, JSON.stringify(options, null, 2));
        console.log('='.repeat(60));

        // 执行路由发现（包含动态分析）
        console.log('\n🔍 Starting comprehensive route discovery...');
        const manifest = await discovery.discoverRoutes(projectPath);

        // 显示结果
        console.log('\n📊 Discovery Results:');
        console.log(`Total routes found: ${manifest.routes.length}`);
        
        // 按来源分组显示
        const routesBySource = {};
        manifest.routes.forEach(route => {
            const source = route.source || 'unknown';
            if (!routesBySource[source]) {
                routesBySource[source] = [];
            }
            routesBySource[source].push(route);
        });

        Object.entries(routesBySource).forEach(([source, routes]) => {
            console.log(`\n📁 ${source.toUpperCase()} (${routes.length} routes):`);
            routes.slice(0, 5).forEach(route => {
                console.log(`  ${route.method || 'GET'} ${route.path} (confidence: ${route.confidence?.toFixed(2) || 'N/A'})`);
            });
            if (routes.length > 5) {
                console.log(`  ... and ${routes.length - 5} more`);
            }
        });

        // 显示动态分析特有的信息
        const dynamicRoutes = manifest.routes.filter(r => r.source === 'dynamic-analysis');
        if (dynamicRoutes.length > 0) {
            console.log('\n🌐 Dynamic Analysis Details:');
            dynamicRoutes.forEach(route => {
                const metadata = route.metadata || {};
                console.log(`  ${route.path}`);
                console.log(`    Status: ${metadata.status || 'N/A'}`);
                console.log(`    Title: ${metadata.title || 'N/A'}`);
                console.log(`    Forms: ${metadata.formsCount || 0}, Links: ${metadata.linksCount || 0}`);
            });
        }

        // React Router分析
        console.log('\n⚛️  React Router Analysis:');
        const reactAnalysis = discovery.analyzeForReactRouter();
        console.log(`Dynamic routes: ${reactAnalysis.dynamicRoutes.length}`);
        console.log(`Static routes: ${reactAnalysis.staticRoutes.length}`);
        console.log(`Nested routes: ${reactAnalysis.nestedRoutes.length}`);
        
        if (reactAnalysis.suggestions.length > 0) {
            console.log('\n💡 Suggestions:');
            reactAnalysis.suggestions.forEach(suggestion => {
                console.log(`  • ${suggestion}`);
            });
        }

        // 导出结果
        const outputPath = path.join(__dirname, '../output/dynamic-manifest.json');
        await discovery.exportManifest(outputPath, projectPath);
        console.log(`\n💾 Manifest exported to: ${outputPath}`);

        // 显示摘要
        console.log('\n📋 Summary:');
        console.log(discovery.generateSummary());

        console.log('\n✅ Dynamic analysis completed successfully!');

    } catch (error) {
        console.error('\n❌ Dynamic analysis failed:', error.message);
        console.error(error.stack);
        process.exit(1);
    }
}

// 独立的应用管理示例
async function applicationManagementExample() {
    const discovery = new RouteDiscovery();
    const projectPath = path.join(__dirname, '../fixtures/source');

    try {
        console.log('\n🚀 Application Management Example');
        console.log('='.repeat(40));

        // 手动启动应用
        console.log('Starting application...');
        const appInfo = await discovery.startApplicationForAnalysis(projectPath);
        console.log(`Application started: ${appInfo.baseUrl}`);
        console.log(`Process ID: ${appInfo.id}`);
        console.log(`PID: ${appInfo.pid}`);

        // 等待一段时间
        console.log('Waiting 10 seconds...');
        await new Promise(resolve => setTimeout(resolve, 10000));

        // 获取运行信息
        const runningApp = discovery.getRunningApplication();
        console.log(`Application status: ${runningApp.status}`);

        // 手动停止应用
        console.log('Stopping application...');
        await discovery.stopApplication();
        console.log('Application stopped');

    } catch (error) {
        console.error('Application management failed:', error.message);
    } finally {
        await discovery.cleanup();
    }
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--app-management')) {
        await applicationManagementExample();
    } else {
        await runDynamicAnalysis();
    }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

process.on('SIGINT', async () => {
    console.log('\n\n🛑 Received SIGINT, cleaning up...');
    process.exit(0);
});

// 运行示例
if (require.main === module) {
    main().catch(error => {
        console.error('Example failed:', error);
        process.exit(1);
    });
}

module.exports = {
    runDynamicAnalysis,
    applicationManagementExample
};
