# 动态分析功能

JSP2React Agent 现在支持动态分析功能，可以自动启动Java应用并使用Puppeteer进行实时页面访问和HTML内容分析。

## 功能概述

动态分析功能包含以下组件：

1. **ApplicationDetector** - 自动检测Maven/Gradle项目类型
2. **ProcessManager** - 管理Java应用进程的启动和停止
3. **DynamicPageAnalyzer** - 使用Puppeteer访问运行中的应用
4. **RouteDiscovery** - 集成动态分析到路由发现流程

## 主要特性

### 🔍 自动应用检测
- 自动识别Maven或Gradle项目
- 提取端口号和上下文路径配置
- 生成适当的启动命令

### 🚀 进程管理
- 启动和停止Java应用
- 监控应用状态和日志
- 自动检测应用就绪状态

### 🌐 动态页面分析
- 使用Puppeteer访问运行中的应用
- 批量分析多个页面
- 提取页面信息（表单、链接、脚本等）
- 保存HTML内容和截图

### 📊 增强的路由发现
- 结合静态和动态分析结果
- 验证路由的实际可访问性
- 提供更准确的置信度评分

## 使用方法

### 基本用法

```javascript
const RouteDiscovery = require('./src/analysis/RouteDiscovery');

const discovery = new RouteDiscovery({
    includeStaticResources: true,
    includeWebXml: true,
    includeAnnotations: true,
    includeDynamicAnalysis: true, // 启用动态分析
    dynamicAnalysisTimeout: 120000, // 2分钟超时
    savePageContents: true,
    outputDir: './output/dynamic-analysis'
});

// 执行完整的路由发现（包含动态分析）
const manifest = await discovery.discoverRoutes('/path/to/java/project');
console.log(`发现 ${manifest.routes.length} 个路由`);
```

### 手动应用管理

```javascript
// 手动启动应用
const appInfo = await discovery.startApplicationForAnalysis('/path/to/project');
console.log(`应用启动: ${appInfo.baseUrl}`);

// 获取运行状态
const runningApp = discovery.getRunningApplication();
console.log(`应用状态: ${runningApp.status}`);

// 手动停止应用
await discovery.stopApplication();
```

### 独立使用组件

```javascript
const ProcessManager = require('./src/process/ProcessManager');
const DynamicPageAnalyzer = require('./src/process/DynamicPageAnalyzer');

// 使用进程管理器
const processManager = new ProcessManager();
const appInfo = await processManager.startApplication('/path/to/project');

// 使用页面分析器
const analyzer = new DynamicPageAnalyzer();
await analyzer.initialize();
const result = await analyzer.analyzePage('http://localhost:8080');
await analyzer.close();
```

## 配置选项

### RouteDiscovery 选项

```javascript
{
    // 基本选项
    includeStaticResources: true,
    includeWebXml: true,
    includeAnnotations: true,
    
    // 动态分析选项
    includeDynamicAnalysis: false,    // 是否启用动态分析
    dynamicAnalysisTimeout: 120000,   // 应用启动超时时间（毫秒）
    savePageContents: true,           // 是否保存页面内容
    outputDir: './output/dynamic-analysis', // 输出目录
    
    // 其他选项
    minConfidence: 0.0,
    deduplicateRoutes: true
}
```

### DynamicPageAnalyzer 选项

```javascript
{
    headless: true,           // 无头模式
    timeout: 30000,          // 页面加载超时
    waitForSelector: 'body', // 等待的选择器
    userAgent: 'Mozilla/5.0...', // 用户代理
    screenshot: false,       // 是否截图
    concurrent: 3,          // 并发分析数量
    delay: 1000            // 请求间隔
}
```

## 运行示例

### 1. 运行动态分析示例

```bash
npm run example:dynamic
```

这将：
- 检测fixtures/source中的Java项目
- 启动应用
- 执行动态路由发现
- 保存分析结果

### 2. 运行应用管理示例

```bash
npm run example:app-management
```

这将演示如何手动管理Java应用的生命周期。

### 3. 运行集成测试

```bash
npm run test:integration
```

注意：集成测试需要实际的Java环境和可运行的项目。

## 输出结果

### 动态分析路由

动态分析发现的路由包含以下信息：

```javascript
{
    path: '/api/users',
    method: 'GET',
    type: 'dynamic',
    source: 'dynamic-analysis',
    confidence: 0.8,
    metadata: {
        status: 200,
        title: 'User Management',
        formsCount: 2,
        linksCount: 15,
        scriptsCount: 3,
        discoveredAt: '2024-01-01T12:00:00.000Z'
    }
}
```

### 保存的文件

动态分析会在输出目录中保存：

- `*.html` - 页面HTML内容
- `*.json` - 页面元数据
- `*.png` - 页面截图（如果启用）

## 故障排除

### 常见问题

1. **应用启动失败**
   - 检查Java环境是否正确配置
   - 确认项目依赖已安装
   - 查看应用日志输出

2. **端口冲突**
   - 确保配置的端口未被占用
   - 检查防火墙设置

3. **Puppeteer问题**
   - 确保Chrome/Chromium已安装
   - 在Docker环境中可能需要额外配置

### 调试模式

启用详细日志：

```javascript
const discovery = new RouteDiscovery({
    includeDynamicAnalysis: true,
    // 其他选项...
});

// 监听进程事件
discovery.processManager.on('processLog', ({ processId, type, message }) => {
    console.log(`[${processId}] ${type}: ${message}`);
});
```

## 性能考虑

- 动态分析会显著增加执行时间
- 建议在CI/CD环境中谨慎使用
- 可以通过调整并发数量和延迟来控制负载
- 大型应用可能需要增加超时时间

## 安全注意事项

- 动态分析会实际启动应用，请确保在安全环境中运行
- 避免在生产环境中运行动态分析
- 注意应用可能产生的副作用（如数据库写入）

## 扩展性

该架构支持以下扩展：

1. **自定义应用检测器** - 支持更多项目类型
2. **自定义页面分析器** - 添加特定的分析逻辑
3. **自定义路由处理器** - 处理特殊的路由模式
4. **集成外部工具** - 如性能监控、安全扫描等
