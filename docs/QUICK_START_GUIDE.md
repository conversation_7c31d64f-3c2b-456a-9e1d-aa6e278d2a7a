# 🚀 进程管理功能快速开始指南

## 📋 前置要求

1. **Node.js环境**: Node.js 14+ 
2. **Java环境**: JDK 8+ (用于运行Java应用)
3. **Maven/Gradle**: 根据项目类型安装相应构建工具

## 🔧 安装依赖

```bash
# 安装项目依赖
npm install

# 验证安装
npm test -- --testPathPattern="ApplicationDetector"
```

## 🎯 基本使用

### 1. 静态分析（推荐开始）

```javascript
const RouteDiscovery = require('./src/analysis/RouteDiscovery');

const discovery = new RouteDiscovery({
    includeStaticResources: true,
    includeWebXml: true,
    includeAnnotations: true,
    includeDynamicAnalysis: false // 先关闭动态分析
});

// 分析Java项目
const manifest = await discovery.discoverRoutes('./path/to/java/project');
console.log(`发现 ${manifest.routes.length} 个路由`);
```

### 2. 应用类型检测

```javascript
const ApplicationDetector = require('./src/process/ApplicationDetector');

const detector = new ApplicationDetector();
const detection = detector.detectApplicationType('./path/to/java/project');

console.log('项目类型:', detection.type);        // 'maven' 或 'gradle'
console.log('启动命令:', detection.startCommand); // 'mvn spring-boot:run'
console.log('端口:', detection.port);            // 8080
console.log('访问地址:', detector.getBaseUrl(detection));
```

### 3. 进程管理

```javascript
const ProcessManager = require('./src/process/ProcessManager');

const manager = new ProcessManager();

// 启动应用
const appInfo = await manager.startApplication('./path/to/java/project');
console.log(`应用启动成功: ${appInfo.baseUrl}`);

// 查看运行状态
const runningApps = manager.getRunningProcesses();
console.log('运行中的应用:', runningApps.length);

// 停止应用
await manager.stopApplication(appInfo.id);
```

### 4. 动态页面分析

```javascript
const DynamicPageAnalyzer = require('./src/process/DynamicPageAnalyzer');

const analyzer = new DynamicPageAnalyzer();
await analyzer.initialize();

// 分析单个页面
const result = await analyzer.analyzePage('http://localhost:8080');
console.log('页面标题:', result.pageInfo.title);
console.log('表单数量:', result.pageInfo.forms.length);

// 批量分析
const urls = ['http://localhost:8080', 'http://localhost:8080/users'];
const results = await analyzer.analyzePages(urls);

await analyzer.close();
```

## 🎮 运行示例

### 示例1: 静态分析测试

```bash
node -e "
const RouteDiscovery = require('./src/analysis/RouteDiscovery');
const discovery = new RouteDiscovery({ includeDynamicAnalysis: false });

discovery.discoverRoutes('./fixtures/source').then(manifest => {
    console.log('发现路由:', manifest.routes.length);
    console.log('应用类型:', discovery.processManager.detector.detectApplicationType('./fixtures/source').type);
}).catch(console.error);
"
```

### 示例2: 完整动态分析

```bash
npm run example:dynamic
```

### 示例3: 应用管理

```bash
npm run example:app-management
```

## 📊 配置选项

### 基础配置

```javascript
const options = {
    // 静态分析选项
    includeStaticResources: true,
    includeWebXml: true,
    includeAnnotations: true,
    
    // 动态分析选项
    includeDynamicAnalysis: true,
    dynamicAnalysisTimeout: 120000,  // 2分钟
    savePageContents: true,
    outputDir: './output/analysis',
    
    // 其他选项
    minConfidence: 0.0,
    deduplicateRoutes: true
};
```

### 高级配置

```javascript
const pageAnalyzerOptions = {
    headless: true,          // 无头浏览器
    timeout: 30000,         // 页面超时
    concurrent: 2,          // 并发数量
    delay: 1000,           // 请求间隔
    screenshot: false,     // 是否截图
    userAgent: 'Custom-Agent'
};

const analyzer = new DynamicPageAnalyzer(pageAnalyzerOptions);
```

## 🔍 故障排除

### 常见问题

1. **Java应用启动失败**
   ```bash
   # 检查Java版本
   java -version
   
   # 检查Maven/Gradle
   mvn -version
   gradle -version
   ```

2. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :8080
   
   # 或者修改应用配置使用其他端口
   ```

3. **Puppeteer安装问题**
   ```bash
   # 重新安装Puppeteer
   npm uninstall puppeteer
   npm install puppeteer
   ```

### 调试模式

```javascript
// 启用详细日志
const discovery = new RouteDiscovery(options);

discovery.processManager.on('processLog', ({ processId, type, message }) => {
    console.log(`[${processId}] ${type}: ${message}`);
});

discovery.processManager.on('applicationReady', (info) => {
    console.log(`应用就绪: ${info.baseUrl}`);
});
```

## 📝 最佳实践

### 1. 渐进式使用

```javascript
// 第一步：静态分析
const manifest1 = await discovery.discoverRoutes(projectPath);

// 第二步：启用动态分析
discovery.options.includeDynamicAnalysis = true;
const manifest2 = await discovery.discoverRoutes(projectPath);
```

### 2. 资源管理

```javascript
try {
    const manifest = await discovery.discoverRoutes(projectPath);
    // 处理结果...
} finally {
    // 确保清理资源
    await discovery.cleanup();
}
```

### 3. 错误处理

```javascript
try {
    const appInfo = await manager.startApplication(projectPath);
    // 使用应用...
} catch (error) {
    if (error.message.includes('timeout')) {
        console.log('应用启动超时，可能需要更长时间');
    } else if (error.message.includes('Unsupported')) {
        console.log('不支持的项目类型');
    }
} finally {
    await manager.stopAllProcesses();
}
```

## 🎯 下一步

1. **阅读详细文档**: [DYNAMIC_ANALYSIS.md](docs/DYNAMIC_ANALYSIS.md)
2. **查看实现总结**: [IMPLEMENTATION_SUMMARY.md](IMPLEMENTATION_SUMMARY.md)
3. **运行集成测试**: `npm run test:integration`
4. **自定义扩展**: 根据项目需求扩展功能

## 💡 提示

- 首次使用建议先运行静态分析验证基本功能
- 动态分析需要实际的可运行Java项目
- 在CI/CD环境中建议关闭动态分析以节省时间
- 大型项目可能需要调整超时和并发设置

## 🆘 获取帮助

如果遇到问题：
1. 查看错误日志和堆栈跟踪
2. 检查Java环境和项目配置
3. 运行单元测试验证组件功能
4. 查看示例代码了解正确用法
