import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Create from './Create';
import Edit from './Edit';
import Error from './Error';
import Post from './Post';
import Posts from './Posts';

export default function AppRouter() {
  return (
    <Router>
      <Routes>
        <Route path="/create" element={<Create />} />
        <Route path="/edit" element={<Edit />} />
        <Route path="/error" element={<Error />} />
        <Route path="/post" element={<Post />} />
        <Route path="/posts" element={<Posts />} />
      </Routes>
    </Router>
  );
}