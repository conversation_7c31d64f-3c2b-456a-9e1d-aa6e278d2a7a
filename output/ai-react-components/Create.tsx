import React from 'react';
import './css/styles.css';

interface CreatePostProps {
  posts?: any[];
  post?: any;
}

const CreatePost: React.FC<CreatePostProps> = ({ posts, post }) => {
  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    // Implement the logic to submit the form data
  };

  return (
    <div>
      <h2>Create new post</h2>
      <form action="/posts" method="post" onSubmit={handleSubmit}>
        <h4>Title:</h4>
        <input className="text_title" type="text" name="title" required />
        <h4>Context:</h4>
        <textarea className="text_title" name="context" required />
        <br />
        <br />
        <input type="submit" name="add" value="Add post" align="center" />
      </form>
    </div>
  );
};

export default CreatePost;