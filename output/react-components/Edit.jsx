import React from 'react';
import './css/styles.css';

export default function Edit(): React.FC {
  return (
    <div>
      {/* Converted from JSP */}
      <h2>Edit post <c:out value="\"{post.title}\""/></h2>
      <form action="/posts/{post.postId}/edit" method="post">
          <h4>Title:</h4>
          <input className="text_title" type="text" name="title" value="{post.title}" required/ />
          <h4>Context:</h4>
          <textarea className="text_title" name="context" required>
              <c:out value="{post.postedText}"/>
          </textarea>
          <br />
          <br />
          <input className="value_center" type="submit" name="save" value="Save"/ />
      </form>
    </div>
  );
}