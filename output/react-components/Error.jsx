import React from 'react';

export default function Error(): React.FC {
  return (
    <div>
      {/* Converted from JSP */}
      <h1>Page Not Found</h1>
      <h3>
          {not empty message && (
              <div>
                <c:out value="{message}"/>
              </div>
            )}
          {empty message && (
              <div>
                <c:out value="Internal error occurred. Please contact administrator."/>
              </div>
            )}
      </h3>
      <a href="" onclick="history.back()">Back to Previous Page</a>
    </div>
  );
}