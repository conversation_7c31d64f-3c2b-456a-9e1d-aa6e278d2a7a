import React from 'react';
import './css/styles.css';

export default function Post(): React.FC {
  return (
    <div>
      {/* Converted from JSP */}
      <h2>
          <c:out value="{post.title}"/>
      </h2>
      <h4>
          <c:out value="{formatter.format(post.postedDate)}"/>
      </h4>
      <p className="format_post">
          <c:out value="{post.postedText}"/>
      </p>
      <div className="max_width_100">
          <form className="buttons_center" action="/posts/{post.postId}/delete" method="post">
              <input type="submit" name="delete" value="Delete"/ />
          </form>
          <form className="buttons_center" action="/posts/{post.postId}/edit" method="get">
              <input type="submit" name="edit" value="Edit"/ />
          </form>
      </div>
    </div>
  );
}