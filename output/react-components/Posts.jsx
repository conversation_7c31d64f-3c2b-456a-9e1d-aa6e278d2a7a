import React from 'react';
import './css/styles.css';

export default function Posts(): React.FC {
  return (
    <div>
      {/* Converted from JSP */}
      <div className="max_width_400">
          {posts.map((item, index) => (
              <div key={index}>
                <h3>{item.title}</h3>
                        <p>{formatter.format(post.postedDate)}</p>
                        <p>
                            {item.postedText}
                        </p>
                        <a href="posts/{item.postId}"> Continue</a>
                        <br />
              </div>
            ))}
      </div>
      <br />
      <form action="/posts" method="get">
          <input type="submit" name="create" value="Create new post"/ />
      </form>
    </div>
  );
}